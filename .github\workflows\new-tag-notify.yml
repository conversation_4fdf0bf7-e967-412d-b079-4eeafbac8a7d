name: new-tag-notify-discord

on:
  push:
    tags:
      - "*"

jobs:
  new-tag-notify-discord:
    runs-on: ubuntu-latest
    steps:
      - name: Set last Tag version
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV
      - name: Echo Tag version
        run: |
          echo $RELEASE_VERSION
      - name: Discord notification
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: ':rocket: New Tag {{ RELEASE_VERSION }} created on **{{ EVENT_PAYLOAD.repository.full_name }}**'
