name: pr-review-notify-discord

on:
  pull_request_review

jobs:
  pr-review-notify-discord:
    runs-on: ubuntu-latest
    steps:
      - name: Discord notification
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: ':eyes: PR review {{ EVENT_PAYLOAD.action }} on **{{ EVENT_PAYLOAD.repository.full_name }}**: {{ EVENT_PAYLOAD.pull_request.html_url }}'
