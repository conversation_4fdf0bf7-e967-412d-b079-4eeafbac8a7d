import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const projectDomain = {
	users: "users",
	documents: "documents",
	"tasks-tracker": "tasks-tracker",
} as const;

export const userAction = {
	create: "create",
	update: "update",
	delete: "delete",
	list: "list",
	read: "read",
	journal: "journal",
	assign: "assign",
	comment: "comment",
	"comment-list": "comment-list",
	subscribe: "subscribe",
	"restore-from-template": "restore-from-template",
	"use-facsimile": "use-facsimile",
	print: "print",
} as const;

export const entityType = {
	task: "task",
	folder: "folder",
	workspace: "workspace",
	document: "document",
} as const;

export const entity = {
	entityType: Type.Enum(entityType),
	entityId: Type.Optional(Type.String({ ...SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })) })),
	isAllow: Type.Optional(Type.Boolean()),
	projectDomain: Type.Enum(projectDomain),
	userAction: Type.Enum(userAction),
};
