import * as AuthDomain from "./auth-domain";
import * as CalendarDomain from "./calendar-domain";
import * as CoordinatorDomain from "./coordinator-domain";
import * as CrmDomain from "./crm-domain";
import * as DiscussionDomain from "./discussion-domain";
import * as EdxDomain from "./edx-domain";
import * as FabricationDomain from "./fabrication-domain";
import * as FileDomain from "./file-domain";
import * as HrmDomain from "./hrm-domain";
import * as JournalDomain from "./journal-domain";
import * as NotificationDomain from "./notification-domain";
import * as OrganizationDomain from "./organization-domain";
import * as PermissionDomain from "./permission-domain";
import * as PrintFormDomain from "./print-form-domain";
import * as TaskTrackerDomain from "./task-tracker-domain";
import * as UserDomain from "./user-domain";
import * as WarehouseDomain from "./warehouse-domain";

export const availableEntities = {
	...AuthDomain.availableEntities,
	...CalendarDomain.availableEntities,
	...CoordinatorDomain.availableEntities,
	...CrmDomain.availableEntities,
	...DiscussionDomain.availableEntities,
	...EdxDomain.availableEntities,
	...FabricationDomain.availableEntities,
	...FileDomain.availableEntities,
	...HrmDomain.availableEntities,
	...JournalDomain.availableEntities,
	...NotificationDomain.availableEntities,
	...OrganizationDomain.availableEntities,
	...PermissionDomain.availableEntities,
	...PrintFormDomain.availableEntities,
	...TaskTrackerDomain.availableEntities,
	...UserDomain.availableEntities,
	...WarehouseDomain.availableEntities,
};
