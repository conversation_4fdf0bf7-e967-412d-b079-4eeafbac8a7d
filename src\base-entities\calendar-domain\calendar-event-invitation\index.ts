import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const systemStatus = {
	accepted: "accepted",
	pending: "pending",
	rejected: "rejected",
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	calendarEventId: Type.String({ ...SchemaHelper.string.uuid() }),
	userId: Type.String({ ...SchemaHelper.string.uuid() }),

	systemStatus: Type.Enum(systemStatus),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
