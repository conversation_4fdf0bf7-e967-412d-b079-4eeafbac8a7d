import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const category = {
	private: "private",
	public: "public",
} as const;

export const repeatStatus = {
	none: "none",
	"every-day": "every-day",
	"every-week": "every-week",
	"every-month": "every-month",
} as const;

export const systemStatus = {
	canceled: "canceled",
	planned: "planned",
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	masterId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	category: Type.Enum(category),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	finishedAt: Type.Integer({ ...SchemaHelper.number.positive }),
	repeatStatus: Type.Enum(repeatStatus),
	repeatToDate: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.positive })),
	startedAt: Type.Integer({ ...SchemaHelper.number.positive }),
	systemStatus: Type.Enum(systemStatus),
	title: Type.String({ ...SchemaHelper.string.title() }),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
