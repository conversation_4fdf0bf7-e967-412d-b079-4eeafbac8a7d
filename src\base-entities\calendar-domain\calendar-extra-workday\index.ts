import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	relevanceDate: Type.String({ ...SchemaHelper.string.date }),
	title: Type.String({ ...SchemaHelper.string.title() }),
	transferDay: Type.Number({ minimum: 1, maximum: 5 }),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
