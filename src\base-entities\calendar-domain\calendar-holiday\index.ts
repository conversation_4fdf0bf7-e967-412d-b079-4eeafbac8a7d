import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	finishedAt: Type.Integer({ ...SchemaHelper.number.positive }),
	startedAt: Type.Integer({ ...SchemaHelper.number.positive }),
	title: Type.String({ ...SchemaHelper.string.title() }),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
