import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const eventState = {
	canceled: "canceled",
	"i-am-invited": "i-am-invited",
	"i-am-not-invited": "i-am-not-invited",
	"i-am-the-author": "i-am-the-author",
	planned: "planned",
} as const;

export const entity = {
	dateFrom: Type.Integer({ ...SchemaHelper.number.positive }),
	dateTo: Type.Integer({ ...SchemaHelper.number.positive }),
	eventState: Type.Enum(eventState),
};
