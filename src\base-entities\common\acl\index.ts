import { Type } from "@sinclair/typebox";

import { create<PERSON><PERSON>, <PERSON>hemaHelper } from "../../../common";

export const actorType = createEnum([
	"user", // Пользователь
	"user-role", // Пользовательская роль
	"department", // Отдел
]);

export const entity = {
	entityId: Type.String({ ...SchemaHelper.string.uuid() }),

	method: Type.String({ ...SchemaHelper.string.name() }),

	actorType: Type.Enum(actorType),
	actorId: Type.String({ ...SchemaHelper.string.uuid() }),

	isAllowed: Type.Boolean(),
	isInherited: Type.Boolean(),
};
