import { Type } from "@sinclair/typebox";

import { create<PERSON>num, <PERSON>hemaHelper } from "../../../common";

export const systemStatus = createEnum(["executed", "prepared", "rolled-back", "committed"]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	idempotencyKey: Type.String({ ...SchemaHelper.string.uuid() }),
	transactionId: Type.String({ ...SchemaHelper.string.uuid() }),

	executor: Type.String({ minLength: 1 }),
	method: Type.String({ minLength: 1 }),
	reason: Type.Optional(Type.String({ minLength: 1 })),

	systemStatus: Type.Enum(systemStatus),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
