import { Type } from "@sinclair/typebox";

import { create<PERSON>num, <PERSON>hemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"created",
	"in-progress",
	"ready-to-commit",
	"rolled-back",
	"committed",
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	idempotencyKey: Type.String({ ...SchemaHelper.string.uuid() }),

	initiator: Type.String({ minLength: 1 }),
	method: Type.String({ minLength: 1 }),

	systemStatus: Type.Enum(systemStatus),
	reason: Type.Optional(Type.String({minLength: 1})),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
