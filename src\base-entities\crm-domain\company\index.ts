import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	createdAt: Type.Number(),
	crmDirectoryId: Type.String({ ...SchemaHelper.string.uuid() }),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	edxId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	title: Type.String({ ...SchemaHelper.string.title() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	tin: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
	kpp: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
};
