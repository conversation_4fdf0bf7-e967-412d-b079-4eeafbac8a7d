import { Type } from "@sinclair/typebox";

import { <PERSON>hemaHelper } from "../../../common/schema-helper";

export const category = {
	email: "email",
	date: "date",
	number: "number",
	"phone-number": "phone-number",
	string: "string",
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	contactPersonId: Type.String({ ...SchemaHelper.string.uuid() }),

	category: Type.Enum(category),
	description: Type.String({ ...SchemaHelper.string.richText() }),
	payload: Type.String({ ...SchemaHelper.string.richText() }),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
