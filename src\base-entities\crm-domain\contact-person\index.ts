import { Type } from "@sinclair/typebox";

import { <PERSON>hemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	companyId: Type.String({ ...SchemaHelper.string.uuid() }),

	birthday: Type.String({ ...SchemaHelper.string.date() }),
	email: Type.String({ ...SchemaHelper.string.email() }),
	fullName: Type.String({ ...SchemaHelper.string.name() }),
	mobilePhone: Type.String({ ...SchemaHelper.string.internationalPhoneNumber() }),

	deletedAt: SchemaHelper.Nullable(Type.Number()),
	isDeleted: Type.Boolean(),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
