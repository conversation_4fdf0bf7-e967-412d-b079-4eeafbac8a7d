import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const systemStatus = {
	accepted: "accepted", // В работе
	canceled: "canceled", // Отменена
	draft: "draft", // Черновик
} as const;

export const entity = {
	amount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма
	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	costAmount: Type.String({ ...SchemaHelper.string.bigint() }),
	createdAt: Type.Number(),
	companyId: Type.String({ ...SchemaHelper.string.uuid() }), // Контрагент
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	managerId: Type.String({ ...SchemaHelper.string.uuid() }), // Ответственный менеджер
	plannedAmount: Type.String({ ...SchemaHelper.string.bigint() }),
	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }),
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (crm-:id)
	systemStatus: Type.Enum(systemStatus), // Статус
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
