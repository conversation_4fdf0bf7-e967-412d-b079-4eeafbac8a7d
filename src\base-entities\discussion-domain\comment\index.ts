import { Type } from "@sinclair/typebox";

import { createEnum, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../common";

import * as CalendarDomain from "../../calendar-domain";
import * as CrmDomain from "../../crm-domain";
import * as HrmDomain from "../../hrm-domain";
import * as TaskTrackerDomain from "../../task-tracker-domain";
import * as WarehouseDomain from "../../warehouse-domain";

export const availableRelativeEntityType = createEnum([
	CalendarDomain.availableEntities["calendar-event"],
	CrmDomain.availableEntities["doc-act-calculation-header"],
	HrmDomain.availableEntities["application-business-trip"],
	HrmDomain.availableEntities["application-paid-vacation"],
	HrmDomain.availableEntities["application-sick-leave"],
	HrmDomain.availableEntities["application-to-head-of-department"],
	HrmDomain.availableEntities["application-unpaid-time-off"],
	HrmDomain.availableEntities["application-overwork"],
	HrmDomain.availableEntities["application-add-scheduled-and-track-time"],
	TaskTrackerDomain.availableEntities["task"],
	TaskTrackerDomain.availableEntities["task-thread"],
	WarehouseDomain.availableEntities["nomenclature-item"],
]);

export const entity = {
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	createdAt: Type.Number(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	entityId: Type.String({ ...SchemaHelper.string.uuid() }),
	entityType: Type.Enum(availableRelativeEntityType),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	isPinned: Type.Boolean(),
	isSolution: Type.Boolean(),
	parentId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	text: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	attachmentIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() })),
	mentionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() })),
};
