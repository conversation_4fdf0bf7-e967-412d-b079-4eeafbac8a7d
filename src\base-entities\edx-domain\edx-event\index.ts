import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const category = {
	"approval-stage-accepted": "approval-stage-accepted", // Стадия визирования принято
	"approval-stage-rejected": "approval-stage-rejected", // Стадия визирования отклонено
	"approval-stage-sent-for-revision": "approval-stage-sent-for-revision", // Стадия визирования отправлено на доработку
	"approval-stage-accepted-with-comment": "approval-stage-accepted-with-comment", // Стадия визирования  принято с комментарием
	"acceptance-stage-accepted": "acceptance-stage-accepted", // Стадия акцепта принято
	"acceptance-stage-rejected": "acceptance-stage-rejected", // Стадия акцепта отклонено
	"acceptance-stage-sent-for-revision": "acceptance-stage-sent-for-revision", // Стадия акцепта отправлено на доработку
	"edx-transaction-added": "edx-transaction-added", // edx transaction добавлен
	"edx-transaction-system-status-changed": "edx-transaction-system-status-changed", // Изменен статус edx transaction
} as const;

export const edxTransactionSystemStatus = {
	draft: "draft", // Черновик
	"ready-to-go": "ready-to-go", // Готово к отправке
	"under-review": "under-review", // На визировании
	"rejected-for-revision": "rejected-for-revision", // Отклонено на доработку
	rejected: "rejected", // Отказ
	"on-acceptance": "on-acceptance", // На принятии
	accepted: "accepted", // Принят
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	edxTransactionId: Type.String({ ...SchemaHelper.string.uuid() }),
	userId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	category: Type.Enum(category),
	comment: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	edxTransactionSystemStatus: SchemaHelper.Nullable(Type.Enum(edxTransactionSystemStatus)),

	createdAt: Type.Number(),
};
