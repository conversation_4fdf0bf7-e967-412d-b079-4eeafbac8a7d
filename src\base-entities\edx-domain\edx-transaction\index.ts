import { Type } from "@sinclair/typebox";

import { createEnum, documentType, domainNames, SchemaHelper } from "../../../common";
import { status as acceptorReviewStatus } from "../acceptor-review";
import { status as vizierReviewStatus } from "../vizier-review";

export const systemStatus = createEnum([
	"draft", // Черновик
	"ready-to-go", // Готово к отправке
	"under-review", // На визировании
	"rejected-for-revision", // Отклонено на доработку
	"rejected", // Отказ
	"on-acceptance", // На принятии
	"accepted", // Принят
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	documentId: Type.String({ ...SchemaHelper.string.uuid() }),

	acceptorReviewIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { minItems: 1, uniqueItems: true }),
	vizierReviewIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),

	acceptorReviewStatus: Type.Enum(acceptorReviewStatus),
	vizierReviewStatus: Type.Enum(vizierReviewStatus),

	acceptedAt: SchemaHelper.Nullable(Type.Number()), // Дата подписания
	documentType: Type.Enum(documentType),
	domainName: Type.Enum(domainNames),
	internalCode: Type.String(),
	systemStatus: Type.Enum(systemStatus), // Статус

	deletedAt: SchemaHelper.Nullable(Type.Number()),
	isDeleted: Type.Boolean(),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
