import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const status = {
	pending: "pending", // Ожидает
	accepted: "accepted", // Принят
	"accepted-with-comment": "accepted-with-comment", // Принят с комментарием
	rejected: "rejected", // Отказ
	rework: "rework", // На доработку
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	documentId: Type.String({ ...SchemaHelper.string.uuid() }),
	vizierId: Type.String({ ...SchemaHelper.string.uuid() }),

	comment: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	status: Type.Enum(status),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
