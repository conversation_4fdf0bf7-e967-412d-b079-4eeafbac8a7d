import { Type } from "@sinclair/typebox";

import { createEnum, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../common";

// import * as FabricationDomain from "../../fabrication-domain";
import * as TaskTrackerDomain from "../../task-tracker-domain";
import * as WarehouseDomain from "../../warehouse-domain";

export const systemStatus = createEnum([
	"planned", // Запланирован
	"in-progress", // В процессе
	"completed", // Завершен
]);

export const processKind = createEnum([
	"fabrication", // Производство
	"internal-movement", // Внутреннее перемещение
]);

export const availableRelativeEntityType = createEnum([
	"doc-work-order-header", // ToDo: решить проблему с импортом FabricationDomain.availableEntities["doc-work-order-header"],
	"fabricated-product",
	TaskTrackerDomain.availableEntities["task"],
	WarehouseDomain.availableEntities["doc-act-fabrication-header"],
	WarehouseDomain.availableEntities["doc-internal-movement-header"],
]);

export const relationKind = createEnum([
	"main-task", // Задача-представитель (может быть только один на БП)
	"main-document", // Документ-представитель (может быть только один на БП)
	"link", // Какая-либо связь
]);

export const relatedEntity = {
	entityId: Type.String({ ...SchemaHelper.string.uuid() }),
	entityType: Type.Enum(availableRelativeEntityType),

	relationKind: Type.Enum(relationKind),
};

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	authorId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	managerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	path: Type.String(SchemaHelper.string.ltree()),

	workOrderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	processKind: Type.Enum(processKind),
	systemStatus: Type.Enum(systemStatus),

	relatedEntities: Type.Array(Type.Object(relatedEntity), { uniqueItems: true }),

	createdAt: Type.Number(),
	completedAt: SchemaHelper.Nullable(Type.Number()),
	updatedAt: Type.Number(),
};
