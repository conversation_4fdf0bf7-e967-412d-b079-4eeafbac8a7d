import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const sourceType = createEnum(["warehouse", "fabrication", "purchase"]);

export const source = Type.Union([
	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType["warehouse"]),
		warehouseId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	}),

	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType["purchase"]),
		companyId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		managerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // Ответственный
	}),

	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType["fabrication"]),
		docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		optionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),
		warehouseId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	}),
]);

export const entity = {
	docWorkOrderHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),

	id: Type.String({ ...SchemaHelper.string.uuid() }),

	path: Type.String(), // LTREE путь, все элементы на первом уровне имеют path равный их id
	orderNumber: Type.Number({ ...SchemaHelper.number.positive() }),

	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	bomVersionId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	optionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),
	source: SchemaHelper.Nullable(source),

	quantity: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })),
	excessQuantity: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })), // Сколько нужно сверх необходимого
};
