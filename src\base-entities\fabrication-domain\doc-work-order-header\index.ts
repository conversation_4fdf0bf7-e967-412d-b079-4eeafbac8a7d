import { Type } from "@sinclair/typebox";

import { SchemaHelper, createEnum } from "../../../common";

export const systemStatus = createEnum([
	"draft", // Черновик
	"in-progress", // В работе
	"canceled", // Отменен
	"completed", // Завершен
]);

export const generalStatus = createEnum([
	"accept-awaiting", // Ожидание согласования
	"payment-awaiting", // Ожидает оплаты
	"in-production", // В производстве
	"shipment-awaiting", // Ожидает отгрузки
	"shipped", // Отгружен
]);

export const paymentStatus = createEnum([
	"not-applicable", // Не применимо
	"not-paid", // Не оплачено
	"partially-paid", // Оплачено частично
	"paid", // Оплачено
]);

export const dealStatus = createEnum([
	"not-applicable", // Не применимо
	"pre-sale", // Пресейл
	"negotiation", // Согласование договора
	"accepted", // Договор согласован
	"signed", // Договор подписан
]);

export const bomStatus = createEnum([
	"accept-awaiting", // Ожидание согласования
	"accepted", // Согласован
	"necessary-reserved", // Все необходимые остатки зарезервированы
]);

export const deficitStatus = createEnum([
	"not-applicable", // Не применимо
	"awaiting", // Ожидание развертывания
	"accept-awaiting", // Ожидание согласования
	"accepted", // Согласован
]);

export const supplyStatus = createEnum([
	"not-applicable", // Не применимо
	"deficit-recorded", // Зафиксирован дефицит к поставке
	"partially-shipped", // Частично отгружен
	"shipped", // Отгружен полностью
]);

/**
 * Статус оплаты поставки
 **/
export const supplyPurchaseStatus = createEnum([
	"not-applicable", // Не применимо
	"not-paid", // Не оплачено
	"partially-paid", // Оплачено частично
	"paid", // Оплачено
]);

/**
 * Статус производства
 */
export const fabricationStatus = createEnum([
	"awaiting", // Ожидание
	"in-progress", // В процессе
	"partially-completed", // Завершено частично
	"completed", // Завершено
	"blocked", // Заблокировано
]);

/**
 * Статус ОТК
 */
export const qualityControlStatus = createEnum([
	"not-applicable", // Не применимо
	"awaiting", // Ожидание готовности изделия
	"in-progress", // В процессе
	"partially-completed", // Завершено частично
	"completed", // Завершено
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (work-order-prefix-:id)
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата

	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	companyId: Type.String({ ...SchemaHelper.string.uuid() }), // Контрагент
	managerId: Type.String({ ...SchemaHelper.string.uuid() }), // Ответственный
	taskTrackerDomainDirectoryId: Type.String({ ...SchemaHelper.string.uuid() }),
	taskTrackerDomainThreadId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	amount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма
	costAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Себестоимость по документу
	plannedAmount: Type.String({ ...SchemaHelper.string.bigint() }),

	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }),
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),

	systemStatus: Type.Enum(systemStatus), // Статус
	generalStatus: Type.Enum(generalStatus), // Основной статус
	paymentStatus: Type.Enum(paymentStatus), // Статус оплат
	dealStatus: Type.Enum(dealStatus), // Статус договора
	bomStatus: Type.Enum(bomStatus), // Статус BOM
	deficitStatus: Type.Enum(deficitStatus), // Статус Дефицита заказа
	supplyStatus: Type.Enum(supplyStatus), // Статус поставки
	supplyPurchaseStatus: Type.Enum(supplyPurchaseStatus), // Статус оплаты поставки
	fabricationStatus: Type.Enum(fabricationStatus), // Статус производства
	qualityControlStatus: Type.Enum(qualityControlStatus), // Статус ОТК

	finalCustomerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
