import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	headerId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	bomId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	fabricatedProductId: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.uuid() }),
	),
	productOptionSet: SchemaHelper.Nullable(
		Type.Array(Type.String({ ...SchemaHelper.string.uuid() })),
	),
	plannedPrice: Type.String({ ...SchemaHelper.string.bigint() }),
	quantity: Type.String({ ...SchemaHelper.string.bigint() }),
	sellingPrice: Type.String({ ...SchemaHelper.string.bigint() }),
	title: Type.String({ ...SchemaHelper.string.title() }),
};
