import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

// ToDo: убрать, уже есть в doc-work-order-header
// Общий
export const generalStatus = {
	draft: "draft", // Черновик
	accepted: "accepted", // В работе
	completed: "completed", // Завершен
	canceled: "canceled", // Отменен
} as const;

// Прогресс
export const progressStatus = {
	awaiting: "awaiting", // Ожидание
	"fabrication-awaiting": "fabrication-awaiting", // Ожидание производства
	"in-fabrication": "in-fabrication", // В производстве
	"shipment-awaiting": "shipment-awaiting", // Ожидает отгрузки
	shipped: "shipped", // Отгружен
} as const;

// Договор
export const dealStatus = {
	"not-applicable": "not-applicable", // Не применимо
	"accept-awaiting": "accept-awaiting", // Ожидание согласования
	signed: "signed", // Договор подписан
} as const;

// Оплата
export const paymentStatus = {
	"not-applicable": "not-applicable", // Не применимо
	"no-invoice": "no-invoice", // Счёт не выставлен
	"payment-awaiting": "payment-awaiting", // Ожидает оплаты
	"partially-paid": "partially-paid", // Оплачено частично
	paid: "paid", // Оплачено
} as const;

// BOM
export const bomStatus = {
	awaiting: "awaiting", // Ожидание
	"partially-reserved": "partially-reserved", // Неполный резерв
	reserved: "reserved", // Полный резерв
} as const;

// Закупка
export const purchaseStatus = {
	"not-applicable": "not-applicable", // Не применимо
	"deficit-recorded": "deficit-recorded", // Зафиксирован дефицит к закупке
	formed: "formed", // Сформирована закупка у поставщика
	"partially-received": "partially-received", // Частично получена
	received: "received", // Получена
} as const;

// Статус производства
export const fabricationStatus = {
	awaiting: "awaiting", // Ожидание
	"in-progress": "in-progress", // В процессе
	"partially-completed": "partially-completed", // Завершено частично
	completed: "completed", // Завершено
} as const;

// Отгрузка
export const supplyStatus = {
	awaiting: "awaiting", // Не применимо
	"partially-shipped": "partially-shipped", // Частично отгружен
	shipped: "shipped", // Отгружен полностью
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	headerId: Type.String({ ...SchemaHelper.string.uuid() }),

	general: Type.Enum(generalStatus), // Общий
	progress: Type.Enum(progressStatus), // Прогресс
	deal: Type.Enum(dealStatus), // Договор
	payment: Type.Enum(paymentStatus), // Оплата
	bom: Type.Enum(bomStatus), // BOM
	purchase: Type.Enum(purchaseStatus), // Закупка
	fabrication: Type.Enum(fabricationStatus), // Статус производства
	supply: Type.Enum(supplyStatus), // Отгрузка

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
