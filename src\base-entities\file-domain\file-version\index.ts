import { Type } from "@sinclair/typebox";

import { <PERSON>hemaHelper } from "../../../common/schema-helper";

export const entity = {
	createdAt: Type.Number(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	fileId: Type.String({ ...SchemaHelper.string.uuid() }),
	fileName: Type.String({ ...SchemaHelper.string.name() }),
	fileSize: Type.Number({ ...SchemaHelper.number.positive() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	mimeType: Type.String({ ...SchemaHelper.string.name() }),
	versionTitle: Type.String({ ...SchemaHelper.string.trim() }),
};
