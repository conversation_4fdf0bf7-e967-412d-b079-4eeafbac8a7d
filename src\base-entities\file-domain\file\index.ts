import { Type } from "@sinclair/typebox";

import { create<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../common";

import * as CalendarDomain from "../../calendar-domain";
import * as CrmDomain from "../../crm-domain";
import * as HrmDomain from "../../hrm-domain";
import * as DiscussionDomain from "../../discussion-domain";
import * as TaskTrackerDomain from "../../task-tracker-domain";
import * as WarehouseDomain from "../../warehouse-domain";
import * as UserDomain from "../../user-domain";
import * as OrganizationDomain from "../../organization-domain";

export const availableRelativeEntityType = createEnum([
	CalendarDomain.availableEntities["calendar-event"],
	CrmDomain.availableEntities["doc-act-calculation-header"],
	HrmDomain.availableEntities["application-business-trip"],
	HrmDomain.availableEntities["application-overwork"],
	HrmDomain.availableEntities["application-paid-vacation"],
	HrmDomain.availableEntities["application-sick-leave"],
	HrmDomain.availableEntities["application-to-head-of-department"],
	HrmDomain.availableEntities["application-unpaid-time-off"],
	HrmDomain.availableEntities["application-add-scheduled-and-track-time"],
	DiscussionDomain.availableEntities["comment"],
	WarehouseDomain.availableEntities["nomenclature-item"],
	WarehouseDomain.availableEntities["doc-act-inventory-header"],
	TaskTrackerDomain.availableEntities["task"],
	TaskTrackerDomain.availableEntities["task-thread"],
	UserDomain.availableEntities["user"],
	OrganizationDomain.availableEntities["department"],
]);

export const entity = {
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	createdAt: Type.Number(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	description: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.richText() }),
	),
	entityId: Type.String({ ...SchemaHelper.string.uuid() }),
	entityType: Type.Enum(availableRelativeEntityType),
	fileName: Type.String({ ...SchemaHelper.string.name() }),
	fileSize: Type.Number({ ...SchemaHelper.number.positive() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	mimeType: Type.String({ ...SchemaHelper.string.name() }),
	title: Type.String({ ...SchemaHelper.string.title() }),
	lastVersionId: Type.String({ ...SchemaHelper.string.uuid() }),
	lastVersionTitle: Type.String({ ...SchemaHelper.string.trim() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
