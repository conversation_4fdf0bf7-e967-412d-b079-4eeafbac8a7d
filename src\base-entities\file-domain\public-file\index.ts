import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const availableFileType = {
	avatar: "avatar",
} as const;

export const entity = {
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	createdAt: Type.Number(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	description: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.richText() }),
	),
	fileType: Type.Enum(availableFileType),
	fileName: Type.String({ ...SchemaHelper.string.name() }),
	fileSize: Type.Number({ ...SchemaHelper.number.positive() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	idempotencyKey: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: <PERSON>.Boolean(),
	mimeType: Type.String({ ...SchemaHelper.string.name() }),
	title: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.title() }),
	),
	updatedAt: Type.Number(),
};
