import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";
import { dataType } from "../../../common/data-type";

export const entity = {
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	bomId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	createdAt: Type.Number(),
	dataType: Type.Enum(dataType),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	fileId: Type.String({ ...SchemaHelper.string.uuid() }),
	actualVersionId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	title: Type.String({ ...SchemaHelper.string.title() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
