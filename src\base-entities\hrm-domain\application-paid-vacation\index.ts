import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const systemStatus = {
	"in-progress": "in-progress", // В работе
	accepted: "accepted", // Принят
	draft: "draft", // Черновик
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	authorId: Type.String({ ...SchemaHelper.string.uuid() }),

	acceptorIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { minItems: 1, uniqueItems: true }),
	vizierIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),

	internalCode: Type.String(), // auto-incremented id (hrm-:id)
	payload: Type.String({ ...SchemaHelper.string.richText() }),
	signatureDate: Type.String({ ...SchemaHelper.string.date() }),
	systemStatus: Type.Enum(systemStatus), // Статус
	vacationStartDate: Type.String({ ...SchemaHelper.string.date() }),
	vacationEndDate: Type.String({ ...SchemaHelper.string.date() }),

	deletedAt: SchemaHelper.Nullable(Type.Number()),
	isDeleted: Type.Boolean(),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
