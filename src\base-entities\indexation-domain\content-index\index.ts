import { Type } from "@sinclair/typebox";
import { SchemaHelper } from "../../../common";

export const contentPreview = Type.Object({
	foundContent: Type.Array(Type.String()),
	contentLength: Type.Number(),
});

export const entity = {
	entityId: Type.String({ ...SchemaHelper.string.uuid() }),
	entityType: Type.String(),
	stringifiedResult: Type.String({ ...SchemaHelper.string.richText() }),
	stringifiedParams: Type.String({ ...SchemaHelper.string.richText() }),
	domain: Type.String(),
	methodName: Type.String(),
	createdAt: Type.Number(),
	updatedAt: Type.Number(),
	contentPreview,
};
