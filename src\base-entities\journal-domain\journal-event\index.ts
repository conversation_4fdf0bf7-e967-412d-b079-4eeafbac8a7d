import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	entityId: Type.String({ ...SchemaHelper.string.uuid() }),
	eventMethod: Type.String({ ...SchemaHelper.string.name() }),
	ipAddress: Type.Union([
		Type.String({ ...SchemaHelper.string.ipv4() }),
		Type.String({ ...SchemaHelper.string.ipv6() }),
	]),
	userId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	domain: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
	isDeleted: Type.Boolean(),
	createdAt: Type.Number(),
};
