import { createNumericEnum } from "../../../common";

export const notificationType = createNumericEnum("NotificationType", {
	1: "other",

	2: "calendar:event-new",
	3: "calendar:event-update",
	4: "calendar:event-cancel",
	5: "calendar:event-delete",

	6: "discussion:answer-new",
	7: "discussion:comment-noted",

	8: "edx:document-sign-new",
	9: "edx:document-review-new",
	10: "edx:document-status-change",

	11: "organization:time-table-generation-available",

	12: "task-tracker:task-overdue",
	13: "task-tracker:task-risk-overdue",
	14: "task-tracker:task-block-link-change",
	15: "task-tracker:task-accept-new",
	16: "task-tracker:task-status-change",
	17: "task-tracker:task-unlock",
	18: "task-tracker:task-link-change",
	19: "task-tracker:task-checklist-change",
	20: "task-tracker:task-assigned-new",
	21: "task-tracker:task-change",
});

export type NotificationType = keyof typeof notificationType.constEnum;
