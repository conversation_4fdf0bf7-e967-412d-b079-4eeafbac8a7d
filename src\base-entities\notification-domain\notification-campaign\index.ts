import { Type } from "@sinclair/typebox";

import * as Common from "../common";
import { createEnum, SchemaHelper } from "../../../common";
import { availableEntities } from "../../available-entities";

export const notificationCampaignStatuses = createEnum([
	"draft",
	"progress",
	"completed",
	"awaiting",
]);

export const notificationCampaignRecipientSystemRoles = createEnum([
	"admin",
	"employee",
	"guest",
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({
		...SchemaHelper.string.trim(),
		minLength: 3,
		maxLength: 2000,
	}),
	message: Type.String({ minLength: 3, maxLength: 65536 }),
	authorId: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.uuid() }),
	),
	scheduledAt: Type.Number(),
	status: Type.Enum(notificationCampaignStatuses),
	isEmailEnabled: Type.Boolean(),
	isInappEnabled: Type.Boolean(),
	isSmsEnabled: Type.Boolean(),
	userRecipientIds: Type.Array(
		Type.String({ ...SchemaHelper.string.uuid() }),
		{
			uniqueItems: true,
		},
	),
	userRoleRecipientIds: Type.Array(
		Type.String({ ...SchemaHelper.string.uuid() }),
		{
			uniqueItems: true,
		},
	),
	departmentRecipientIds: Type.Array(
		Type.String({ ...SchemaHelper.string.uuid() }),
		{
			uniqueItems: true,
		},
	),
	processedAt: SchemaHelper.Nullable(Type.Number()),
	recipientSystemRoles: SchemaHelper.Nullable(
		Type.Array(
			Type.Enum(notificationCampaignRecipientSystemRoles),
			{
				uniqueItems: true,
				minItems: 1,
			},
		),
	),

	entityType: SchemaHelper.Nullable(Type.Enum(availableEntities)),
	entityId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	notificationType: SchemaHelper.Nullable(Type.Enum(Common.notificationType.constEnum)),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),

	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
};
