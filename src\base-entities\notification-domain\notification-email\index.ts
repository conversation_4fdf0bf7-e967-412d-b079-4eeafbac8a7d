import { Type } from "@sinclair/typebox";

import { <PERSON>hemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	message: Type.String({ ...SchemaHelper.string.richText() }),
	subject: Type.String({ ...SchemaHelper.string.name() }),
	from: Type.String({ ...SchemaHelper.string.name() }),
	scheduledAt: SchemaHelper.Nullable(Type.Number()),
	status: Type.Number(),
	usersAmountToSchedule: Type.Number(),
	usersAmountToSent: Type.Number(),
	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
