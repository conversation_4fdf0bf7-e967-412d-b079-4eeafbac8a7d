import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	message: Type.String({ minLength: 3, maxLength: 65536 }),
	authorId: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.uuid() }),
	),
	recipientId: Type.String({ ...SchemaHelper.string.uuid() }),
	scheduledAt: Type.Number(),
	entityType: SchemaHelper.Nullable(Type.String()),
	entityId: SchemaHelper.Nullable(
		Type.String({ ...SchemaHelper.string.uuid() }),
	),
	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: Type.Number(),
	viewedAt: SchemaHelper.Nullable(Type.Number()),
	archivedAt: SchemaHelper.Nullable(Type.Number()),
};
