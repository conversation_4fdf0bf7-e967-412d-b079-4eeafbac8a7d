import { Type } from "@sinclair/typebox";

import { <PERSON>hemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	message: Type.String({ minLength: 3, maxLength: 210 }),
	from: Type.String({ ...SchemaHelper.string.name() }),
	scheduledAt: SchemaHelper.Nullable(Type.Number()),
	userRecipientIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
		minItems: 0,
	}),
	status: Type.Number(),
	usersAmountToSchedule: Type.Number(),
	usersAmountToSent: Type.Number(),
	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
