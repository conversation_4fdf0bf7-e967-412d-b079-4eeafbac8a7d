import { Type } from "@sinclair/typebox";

import * as Common from "../common";
import { SchemaHelper } from "../../../common";

export const typeSetting = {
	type: Type.Enum(Common.notificationType.constEnum),

	isInappEnabled: Type.Boolean(),
	isEmailEnabled: Type.Boolean(),
};

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	typeSettings: Type.Array(SchemaHelper.StrictObject(typeSetting), { uniqueItems: true }),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
