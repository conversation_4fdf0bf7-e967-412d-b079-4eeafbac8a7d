import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	managerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	parentId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	position: Type.String(),
	title: Type.String({ ...SchemaHelper.string.title() }),

	isHidden: Type.Boolean(),

	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
};
