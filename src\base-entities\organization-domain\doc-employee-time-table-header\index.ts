import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const systemStatus = {
	accepted: "accepted", // В работе
	draft: "draft", // Черновик
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	departmentId: Type.String({ ...SchemaHelper.string.uuid() }),

	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	acceptedBy: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	month: Type.Number({minimum: 1, maximum: 12}),
	year: Type.Number({minimum: 2000, maximum: 2099}),

	systemStatus: Type.Enum(systemStatus),

	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
