import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	docEmployeeTimeTableHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	employeeId: Type.String({ ...SchemaHelper.string.uuid() }),

	absenseHours: Type.Number(),
	breakHours: Type.Number(),
	workHours: Type.Number(),
	overWorkHours: Type.Number(),
	workDayCounter: Type.Number(),
	totalDayCounter: Type.Number(),

	factWorkHours: SchemaHelper.Nullable(Type.Number()),
	factOverWorkHours: SchemaHelper.Nullable(Type.Number()),

	updatedAt: SchemaHelper.Nullable(Type.Number()),
	updatedBy: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
};
