import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const workType = {
	"5/2": "5/2",
	"2/2": "2/2",
} as const;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	employeeId: Type.String({ ...SchemaHelper.string.uuid() }),

	endTime: Type.String({ ...SchemaHelper.string.time() }),
	isConsideringHolidays: Type.Boolean(),
	startDate: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.date() })),
	startTime: Type.String({ ...SchemaHelper.string.time() }),
	workType: Type.Enum(workType),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
