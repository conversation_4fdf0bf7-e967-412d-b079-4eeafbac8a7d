import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	workScheduleTemplateId: Type.String({ ...SchemaHelper.string.uuid() }),

	dayNumber: Type.Integer(),
	endTime: Type.String({ ...SchemaHelper.string.time() }),
	startTime: Type.String({ ...SchemaHelper.string.time() }),
};
