import { Type } from "@sinclair/typebox";

import { documentType, SchemaHelper } from "../../../common";

export const tagType = {
	array: "array",
	object: "object",
	string: "string",
} as const;

export const tag = Type.Recursive((This) => Type.Object({
	description: Type.String({ ...SchemaHelper.string.richText() }),
	innerTags: Type.Optional(Type.Union([
		Type.String({ ...SchemaHelper.string.name() }),
		Type.Array(Type.Record(Type.String(), This)),
		Type.Record(Type.String(), This),
	])),
	tagType: Type.Enum(tagType),
}, { additionalProperties: false }), { $id: "tag" });

export const entity = {
	documentType: Type.Enum(documentType),
	tags: Type.Record(Type.String({ ...SchemaHelper.string.name() }), tag),
};
