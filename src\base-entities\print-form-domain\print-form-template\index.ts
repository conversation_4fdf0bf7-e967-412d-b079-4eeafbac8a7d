import { Type } from "@sinclair/typebox";

import { documentType, SchemaHelper } from "../../../common";

export const orientation = {
	landscape: "landscape",
	portrait: "portrait",
} as const;

export const entity = {
	createdAt: Type.Number(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
	documentType: Type.Enum(documentType),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	rawText: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richTextExtended() })),
	title: Type.String({ ...SchemaHelper.string.title() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	orientation: Type.Enum(orientation),
};
