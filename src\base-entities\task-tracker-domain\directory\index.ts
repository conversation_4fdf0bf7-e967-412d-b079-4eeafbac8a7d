import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	title: Type.String({ ...SchemaHelper.string.title() }),
	workspaceId: Type.String({ ...SchemaHelper.string.uuid() }),

	editorIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),
	observerIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),

	taskClusterTemplateId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
};
