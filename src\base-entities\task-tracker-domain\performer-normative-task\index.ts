import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	performerId: Type.String({ ...SchemaHelper.string.uuid() }),
	taskId: Type.String({ ...SchemaHelper.string.uuid() }),

	endDateTime: Type.Integer({ ...SchemaHelper.number.positive }),
	orderNumber: Type.Integer({ ...SchemaHelper.number.positive }),
	startDateTime: Type.Integer({ ...SchemaHelper.number.positive }),
};
