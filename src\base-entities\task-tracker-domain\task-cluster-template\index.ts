import { Type } from "@sinclair/typebox";

import { createEnum, Schema<PERSON>elper } from "../../../common";

export const taskClusterTemplate = createEnum([
	"task",
	"task-thread",
	"workspace",
	"directory",
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({ ...SchemaHelper.string.title() }),

	type: Type.Enum(taskClusterTemplate),

	authorId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	taskId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	taskThreadId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	workspaceId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	directoryId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // currently used only to not return BOM-generated TCTs in prepared list

	deletedAt: SchemaHelper.Nullable(Type.Number()),
	isDeleted: Type.Boolean(),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
