import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	taskId: Type.String({ ...SchemaHelper.string.uuid() }),
	workspaceCustomFieldId: Type.String({ ...SchemaHelper.string.uuid() }),

	value: Type.String({ ...SchemaHelper.string.richText() }),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
