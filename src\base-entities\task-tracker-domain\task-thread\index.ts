import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common";

export const entity = {
	directoryId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	isDeleted: Type.Boolean(),
	prefix: Type.String({
		maxLength: 12,
		minLength: 1,
		pattern: "^\\S{1,12}$",
		transform: ["trim"],
	}),
	title: Type.String({ ...SchemaHelper.string.title() }),

	editorIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),
	observerIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),

	taskClusterTemplateId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
};
