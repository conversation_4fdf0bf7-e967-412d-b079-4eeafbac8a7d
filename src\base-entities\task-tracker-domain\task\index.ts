import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const priority = createEnum(["very-hight", "hight", "medium", "normal", "low"]);

export const systemStatus = createEnum([
	"planned", // запланирована
	"assigned", // назначена
	"paused", // на паузе
	"blocked", // заблокирована
	"started", // начата
	"in-progress", // в работе
	"completed", // выполнена
	"closed", // закрыта
	"declined", // отклонена
]);

export const checklist = SchemaHelper.Nullable(
	Type.Array(
		SchemaHelper.StrictObject({
			isDone: Type.Boolean(),
			title: Type.String({ ...SchemaHelper.string.trim(), maxLength: 80 }),
		}),
		{ uniqueItems: true, minItems: 1 },
	),
);

export const blockLink = SchemaHelper.StrictObject({
	predecessorTaskId: Type.String({ ...SchemaHelper.string.uuid() }),
	successorTaskId: Type.String({ ...SchemaHelper.string.uuid() }),
	isBlocking: Type.Boolean(),
});

export const normative = SchemaHelper.StrictObject({
	startDate: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.nonNegative() })),
	endDate: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.nonNegative() })),
	value: SchemaHelper.Nullable(Type.Number({ ...SchemaHelper.number.nonNegative() })),
});

export const laborIntensity = {
	startDate: Type.Integer({ ...SchemaHelper.number.nonNegative() }),
	endDate: Type.Integer({ ...SchemaHelper.number.nonNegative() }),
	value: Type.Number({ ...SchemaHelper.number.nonNegative() }),
	totalSpentTime: Type.Number({ ...SchemaHelper.number.nonNegative() }),
	isFixed: Type.Boolean(),
};

export const totalNormative = SchemaHelper.StrictObject({
	startDate: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.nonNegative() })),
	endDate: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.nonNegative() })),
});

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	performerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	taskThreadId: Type.String({ ...SchemaHelper.string.uuid() }),

	blockLinks: Type.Array(blockLink, { uniqueItems: true }),

	editorIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),
	observerIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),
	relatedLinkTaskIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),
	tagIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),

	acceptorIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),
	acceptors: Type.Array(
		SchemaHelper.StrictObject({
			userId: Type.String({ ...SchemaHelper.string.uuid() }),
			isAccepted: Type.Boolean(),
		}),
		{ uniqueItems: true },
	),

	checklist,
	closedAt: SchemaHelper.Nullable(Type.Number()),
	deadlineAt: SchemaHelper.Nullable(Type.Number()),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richTextExtended() })),
	fixedStartedAt: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.nonNegative() })),
	isFixed: Type.Boolean(),
	isNormative: Type.Boolean(),
	path: Type.String({ ...SchemaHelper.string.richText() }),
	prefix: Type.String({ ...SchemaHelper.string.richText() }),
	priority: Type.Enum(priority),
	scheduledTime: SchemaHelper.Nullable(Type.Integer({ ...SchemaHelper.number.nonNegative() })),
	spentTime: Type.Integer({ ...SchemaHelper.number.nonNegative() }),
	startedAt: SchemaHelper.Nullable(Type.Number()),
	systemStatus: Type.Enum(systemStatus),
	title: Type.String({ ...SchemaHelper.string.title() }),
	taskClusterTemplateId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	normative,
	laborIntensity: SchemaHelper.StrictObject(laborIntensity),
	totalNormative,
	totalLaborIntensity: Type.Number({ ...SchemaHelper.number.nonNegative() }),

	isOverdue: Type.Boolean(),
	isUnderRiskOverdue: Type.Boolean(),
	isProblematic: Type.Boolean(),

	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
