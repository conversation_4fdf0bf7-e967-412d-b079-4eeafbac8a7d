import { Type } from "@sinclair/typebox";

import { create<PERSON>num, <PERSON>hema<PERSON>el<PERSON> } from "../../../common";

export const entityType = createEnum(["workspace", "directory", "thread", "task"]);
export const eventType = createEnum([
	"task-updated",
	"file-uploaded",
	"comment-created",
	"blocking-link-changed",
	"tags-changed",
	"acceptors-changed",
	"performer-changed",
	"editors-changed",
	"observers-changed",
	"workload-changed",
	"check-list-changed",
	"custom-fields-changed",
	"link-changed",
	"blocked-link-changed",
	"spent-time-changed",
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	actorId: Type.String({ ...SchemaHelper.string.uuid() }),

	entityId: Type.String({ ...SchemaHelper.string.uuid() }),
	entityType: Type.Enum(entityType),

	eventType: Type.Enum(eventType),

	counter: Type.Integer({ ...SchemaHelper.number.nonNegative() }),
};
