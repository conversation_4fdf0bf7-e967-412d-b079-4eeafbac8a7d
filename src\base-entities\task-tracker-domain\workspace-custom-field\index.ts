import { Type } from "@sinclair/typebox";

import { create<PERSON>num, <PERSON>hema<PERSON>el<PERSON> } from "../../../common";

export const category = createEnum([
	"boolean",
	"date",
	"number",
	"string",
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	workspaceId: Type.String({ ...SchemaHelper.string.uuid() }),

	category: Type.Enum(category),
	title: Type.String({ ...SchemaHelper.string.title() }),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
