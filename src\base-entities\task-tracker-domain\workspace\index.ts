import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({ ...SchemaHelper.string.title() }),

	deletedAt: SchemaHelper.Nullable(Type.Number()),
	isDeleted: Type.Boolean(),

	editorIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),
	observerIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), {
		uniqueItems: true,
	}),

	taskClusterTemplateId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
