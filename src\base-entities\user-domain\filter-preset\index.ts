import { Type } from "@sinclair/typebox";

import { <PERSON>hemaHelper } from "../../../common/schema-helper";

const sectionType = {
	TASK_TRACKER: "TASK_TRACKER",
} as const;

export const entity = {
	createdAt: Type.Number(),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	title: Type.String({ ...SchemaHelper.string.title() }),
	updatedAt: Type.Number(),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	section: Type.Enum(sectionType),
	method: Type.String({ minLength: 1, maxLength: 255 }),
	filterQuery: Type.String({ minLength: 1, maxLength: 1024 * 64 }),
};
