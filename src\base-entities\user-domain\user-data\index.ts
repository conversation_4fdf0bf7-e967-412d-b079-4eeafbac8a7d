import { Type } from "@sinclair/typebox";

import { create<PERSON>num, SchemaHelper } from "../../../common";

export const category = createEnum(["address", "date", "phone-number"]);

export const entity = {
	category: Type.Enum(category),
	createdAt: Type.Number(),
	description: Type.String({ ...SchemaHelper.string.richText() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	payload: Type.String({ ...SchemaHelper.string.richText() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	userId: Type.String({ ...SchemaHelper.string.uuid() }),
};
