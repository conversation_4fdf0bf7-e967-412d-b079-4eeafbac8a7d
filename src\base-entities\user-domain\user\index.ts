import { Type } from "@sinclair/typebox";

import { SchemaHelper, createEnum } from "../../../common";

export const registrationStatus = createEnum([
	"awaiting-confirmation",
	"registration-confirmed",
	"something-went-wrong",
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	systemRoleId: Type.String({ ...SchemaHelper.string.uuid() }),

	avatarLink: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
	birthday: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.date() })),
	email: Type.String({ ...SchemaHelper.string.email() }),
	firstName: Type.String({ ...SchemaHelper.string.name() }),
	fullName: Type.String({ ...SchemaHelper.string.name() }),
	hrNumber: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.digits() })),
	isBlocked: Type.Boolean(),
	mobilePhone: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.russianMobilePhoneNumber() })),
	lastName: Type.String({ ...SchemaHelper.string.name() }),
	patronymic: Type.String({ ...SchemaHelper.string.name() }),
	registrationStatus: SchemaHelper.Nullable(Type.Enum(registrationStatus)),
	tin: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.digits() })),

	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
