import { createEnum } from "../../common";

export const availableEntities = createEnum([
	"classifier-category",
	"doc-act-fabrication-header",
	"doc-act-fabrication-item",
	"doc-act-inventory-header",
	"doc-act-inventory-item",
	"doc-act-supplier-order-header",
	"doc-act-supplier-order-item",
	"doc-act-work-order-purchase-header",
	"doc-act-work-order-purchase-item",
	"doc-bill-of-material-header",
	"doc-bill-of-material-item",
	"doc-internal-movement-header",
	"doc-internal-movement-item",
	"doc-invoice-purchase-header",
	"doc-invoice-purchase-item",
	"doc-invoice-sale-header",
	"doc-invoice-sale-item",
	"fabricated-product-option",
	"fabricated-product-option-group",
	"nomenclature-item",
	"incoming-serie",
	"supplier-nomenclature-item",
	"warehouse",
	"option-adjust-header",
	"option-adjust-item",
	"nomenclature-item-analogue",
	"fabricated-product",
	"doc-bill-of-material-header",
	"doc-bill-of-material-item",
	"serial-number",
]);
