import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"draft", // Черновик
	"in-progress", // В процессе
	"completed", // Завершен
]);

export const entity = {
	authorId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (whs001-:id)
	systemStatus: Type.Enum(systemStatus), // Статус

	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата
	warehouseId: Type.String({ ...SchemaHelper.string.uuid() }), // Склад-обработчик

	workOrderHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // Связь с Заказом

	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	bomVersionId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	optionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),

	fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	totalCostAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма по себестоимости

	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
