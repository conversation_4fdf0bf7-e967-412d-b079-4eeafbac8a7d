import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const sourceType = createEnum(["warehouse", "fabrication", "purchase", "internal-movement"]);

export const entity = {
	docActFabricationHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	costPrice: Type.String({ ...SchemaHelper.string.bigint() }),
	quantity: Type.String({ ...SchemaHelper.string.bigint() }),
	selectedIncomingSeries: SchemaHelper.Nullable(
		Type.Array(
			SchemaHelper.StrictObject({
				incomingSerieId: Type.String({ ...SchemaHelper.string.uuid() }),
				quantity: Type.String({ ...SchemaHelper.string.bigint() }),
			}),
		),
	),

	sourceType: Type.Enum(sourceType),
	businessProcessId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	orderNumber: Type.Number({ ...SchemaHelper.number.positive() }),
};
