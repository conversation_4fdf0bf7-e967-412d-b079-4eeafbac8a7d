import { Type } from "@sinclair/typebox";

import { createNumericEnum, SchemaHelper } from "../../../common";

export const systemStatus = createNumericEnum("DocActInventoryHeaderSystemStatus", {
	1: "draft", // Черновик
	2: "in-progress", // В работе
	3: "accepted", // Завершен
});

export const entity = {
	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	totalCostAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма прихода
	createdAt: Type.Number(),
	classifierCategoryId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	companyId: Type.String({ ...SchemaHelper.string.uuid() }), // Контрагент
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }), // Число наименований
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (crm-:id)
	systemStatus: Type.Enum(systemStatus.constEnum), // Статус
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	warehouseId: Type.String({ ...SchemaHelper.string.uuid() }),
	relatedDocInvoicePurchaseHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	relatedDocInvoiceSaleHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
};
