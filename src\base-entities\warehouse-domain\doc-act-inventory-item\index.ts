import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	docActInventoryHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	costPrice: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })),
	originalQuantity: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })), // Количество по остатку в момент акцептования инвентаризации
	quantity: Type.String({ ...SchemaHelper.string.bigint() }),
	options: Type.Array(
		SchemaHelper.StrictObject({
			id: Type.String({ ...SchemaHelper.string.uuid() }),
			headerId: Type.String({ ...SchemaHelper.string.uuid() }),
		}),
		{ uniqueItems: true },
	),
	selectedIncomingSeries: SchemaHelper.Nullable(
		Type.Array(
			SchemaHelper.StrictObject({
				incomingSerieId: Type.String({ ...SchemaHelper.string.uuid() }),
				quantity: Type.String({ ...SchemaHelper.string.bigint() }),
			}),
		),
	),
	orderNumber: Type.Number({ ...SchemaHelper.number.positive() }),
};
