import { Type } from "@sinclair/typebox";

import { createNumeric<PERSON>num, SchemaHelper } from "../../../common";

export const systemStatus = createNumericEnum("DocActSupplierOrderHeaderSystemStatus", {
	1: "draft",
	2: "in-progress",
	3: "completed",
	4: "declined",
});

export const entity = {
	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),

	managerId: Type.String({ ...SchemaHelper.string.uuid() }),
	companyId: Type.String({ ...SchemaHelper.string.uuid() }),
	warehouseId: Type.String({ ...SchemaHelper.string.uuid() }),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),

	id: Type.String({ ...SchemaHelper.string.uuid() }),
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата
	expectedDate: Type.String({ ...SchemaHelper.string.date() }), // Ожидаемая дата поставки
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (crm-:id)
	systemStatus: Type.Enum(systemStatus.constEnum), // Статус

	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }), // Число наименований
	totalCostAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма закупки
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	totalReceivedQuantity: Type.String({ ...SchemaHelper.string.bigint() }),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
