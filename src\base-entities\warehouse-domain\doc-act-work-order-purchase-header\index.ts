import { Type } from "@sinclair/typebox";

import { createN<PERSON>ric<PERSON>num, SchemaHelper } from "../../../common";

export const systemStatus = createNumericEnum("DocActWorkOrderPurchaseHeaderSystemStatus", {
	1: "draft", // Черновик
	2: "in-progress", // В работе
	3: "completed", // Завершен
});

export const entity = {
	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),

	managerId: Type.String({ ...SchemaHelper.string.uuid() }),
	docWorkOrderHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
	taskId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // ToDo: после интеграции с таск-трекером должна стать обязательным полем

	id: Type.String({ ...SchemaHelper.string.uuid() }),
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (crm-:id)
	systemStatus: Type.Enum(systemStatus.constEnum), // Статус

	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }), // Число наименований

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
