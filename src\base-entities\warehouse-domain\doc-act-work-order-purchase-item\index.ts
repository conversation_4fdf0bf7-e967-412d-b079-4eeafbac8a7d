import { Type } from "@sinclair/typebox";

import { SchemaHelper, specItemSection } from "../../../common";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	headerId: Type.String({ ...SchemaHelper.string.uuid() }),
	orderNumber: Type.Number({ ...SchemaHelper.number.positive() }),

	section: Type.Enum(specItemSection.constEnum),

	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	options: Type.Array(
		SchemaHelper.StrictObject({
			id: Type.String({ ...SchemaHelper.string.uuid() }),
			headerId: Type.String({ ...SchemaHelper.string.uuid() }),
		}),
		{ uniqueItems: true },
	),

	requiredQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	notCoveredQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	receivedQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
};
