import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"draft", // Черновик (начальный статус)
	"accepted", // Согласовано
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	taskClusterTemplateId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	versionTitle: Type.String({ ...SchemaHelper.string.title() }),
	versionNumber: Type.Number({ ...SchemaHelper.number.nonNegative() }),

	isActualVersion: Type.Boolean(),

	systemStatus: Type.Enum(systemStatus),

	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedBy: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
