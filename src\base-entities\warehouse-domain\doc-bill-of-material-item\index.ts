import { Static, Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper, specItemSection } from "../../../common";

export const sourceType = createEnum(["warehouse", "fabrication", "purchase"]);

export const source = Type.Union([
	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType["warehouse"]),
		warehouseId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	}),

	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType["purchase"]),
		companyId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		managerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // Ответственный
	}),

	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType["fabrication"]),
		warehouseId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	}),
]);

export type SourceType = keyof typeof sourceType;
export type Source = Static<typeof source>;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	docBillOfMaterialHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),

	section: Type.Enum(specItemSection.constEnum),

	source: SchemaHelper.Nullable(source),

	nomenclatureItemId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	nomenclatureItemDocBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	quantity: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })),

	eskdTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title(), minLength: 1 })),
	nonFormalizedTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title(), minLength: 1 })),

	notice: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.trim(), minLength: 1 })),

	options: Type.Array(
		SchemaHelper.StrictObject({
			id: Type.String({ ...SchemaHelper.string.uuid() }),
			headerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		}),
		{ uniqueItems: true },
	),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
