import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"draft", // Черновик
	"reserved", // Зарезервирован
	"forwarded", // Отправлен
	"completed", // Завершен
]);

export const entity = {
	authorId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (whs001-:id)
	systemStatus: Type.Enum(systemStatus), // Статус

	sourceWarehouseId: Type.String({ ...SchemaHelper.string.uuid() }), // Склад отправитель
	destinationWarehouseId: Type.String({ ...SchemaHelper.string.uuid() }), // Склад получатель
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата

	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }), // Число наименований
	totalCostAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма по себестоимости
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),

	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
