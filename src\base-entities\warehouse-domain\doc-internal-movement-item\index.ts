import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	docInternalMovementHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	costPrice: Type.String({ ...SchemaHelper.string.bigint() }),
	quantity: Type.String({ ...SchemaHelper.string.bigint() }),
	selectedIncomingSeries: SchemaHelper.Nullable(
		Type.Array(
			SchemaHelper.StrictObject({
				incomingSerieId: Type.String({ ...SchemaHelper.string.uuid() }),
				quantity: Type.String({ ...SchemaHelper.string.bigint() }),
			}),
		),
	),
	orderNumber: Type.Number({ ...SchemaHelper.number.positive() }),
};
