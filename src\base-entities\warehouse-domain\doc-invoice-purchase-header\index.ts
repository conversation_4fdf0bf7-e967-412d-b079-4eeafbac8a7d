import { Type } from "@sinclair/typebox";

import { createEnum, createNumericEnum, documentType, SchemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"accepted", // В работе
	"draft", // Черновик
]);

export const basisDocumentType = createNumericEnum("DocActInvoicePurchaseBasisDocumentType", {
	1: documentType.whs004,
	2: documentType.whs011,
});

export const entity = {
	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	totalCostAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма прихода
	createdAt: Type.Number(),
	companyId: Type.String({ ...SchemaHelper.string.uuid() }), // Контрагент
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }), // Число наименований
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }), // Дата
	systemId: Type.String({ ...SchemaHelper.string.trim() }), // auto-incremented id (crm-:id)
	systemStatus: Type.Enum(systemStatus), // Статус
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	warehouseId: Type.String({ ...SchemaHelper.string.uuid() }),
	basisDocument: SchemaHelper.Nullable(
		SchemaHelper.StrictObject({
			docHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
			docType: Type.Enum(basisDocumentType.constEnum),
		}),
	),
};
