import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	docInvoicePurchaseHeaderId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),

	fabricatedProductId:  SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	options: SchemaHelper.Nullable(
		Type.Array(
			SchemaHelper.StrictObject({
				id: Type.String({ ...SchemaHelper.string.uuid() }),
				headerId: Type.String({ ...SchemaHelper.string.uuid() }),
			}),
			{ uniqueItems: true },
		),
	),

	costPrice: Type.String({ ...SchemaHelper.string.bigint() }),
	quantity: Type.String({ ...SchemaHelper.string.bigint() }),
	supplierTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	orderNumber: Type.Number({ ...SchemaHelper.number.positive() }),
};
