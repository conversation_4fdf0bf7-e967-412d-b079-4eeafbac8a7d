import { Type } from "@sinclair/typebox";

import { createEnum, SchemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"accepted", // В работе
	"draft", // Черновик
]);

export const entity = {
	acceptedAt: SchemaHelper.Nullable(Type.Number()),
	acceptedById: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	authorId: Type.String({ ...SchemaHelper.string.uuid() }),
	totalCostAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма себестоимости
	createdAt: Type.Number(),
	companyId: Type.String({ ...SchemaHelper.string.uuid() }),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	positionCount: Type.Integer({ ...SchemaHelper.number.nonNegative() }), // Число наименований
	totalPlanAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма плановой стоимости
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }),
	totalSaleAmount: Type.String({ ...SchemaHelper.string.bigint() }), // Сумма фактической стоимости
	systemId: Type.String({ ...SchemaHelper.string.trim() }),
	systemStatus: Type.Enum(systemStatus),
	totalQuantity: Type.String({ ...SchemaHelper.string.bigint() }),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
	warehouseId: Type.String({ ...SchemaHelper.string.uuid() }),
};
