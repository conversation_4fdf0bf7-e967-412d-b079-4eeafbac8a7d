import { Type } from "@sinclair/typebox";

import { createEnum, Schema<PERSON>elper } from "../../../common";

const type = createEnum(["exclusive", "additive"]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({ ...SchemaHelper.string.title() }),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title() })),

	type: Type.Enum(type),
	isSelectedValueRequired: Type.Boolean(),

	optionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() })),

	isHidden: Type.Boolean(),

	orderNumber: Type.Integer(),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),

	isDeleted: Type.Bo<PERSON>(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
};
