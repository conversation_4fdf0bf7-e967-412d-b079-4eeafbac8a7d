import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	groupId: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({ ...SchemaHelper.string.title() }),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title() })),
	mnemonics: Type.String({ minLength: 1, maxLength: 10 }),
	priceModifier: Type.String({ ...SchemaHelper.string.bigint() }),

	requiredOptionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),
	incompatibleOptionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),

	orderNumber: Type.Integer(),

	isHidden: Type.Boolean(),

	actualVersionId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),

	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
};
