import { Type } from "@sinclair/typebox";

import { createEnum, documentType, SchemaHelper } from "../../../common";

export const systemStatus = createEnum([
	"in-progress", // В процессе
	"fabricated", // Произведено
	"disassembled", // Разукомплектовано
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	serialNumber: SchemaHelper.Nullable(Type.String({ minLength: 1 })),

	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }), // Корневой nomenclature-item

	incomingDocHeaderId: Type.String({ ...SchemaHelper.string.uuid() }), // Идентификатор документа, по которому приняли издели
	incomingDocType: Type.Enum(documentType),

	docWorkOrderHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // Заказ
	docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // Версия BOM
	docActDisassemblingHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid })), // Разукомплектация
	docInvoiceSaleHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })), // Отгрузка

	qualityControlProcessId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	fabricatedAt: SchemaHelper.Nullable(Type.Number()), // Дата производства
	disassembledAt: SchemaHelper.Nullable(Type.Number()), // Дата разукомплектации
	shippedAt: SchemaHelper.Nullable(Type.Number()), // Дата отгрузки

	systemStatus: Type.Enum(systemStatus),

	optionIds: Type.Array(Type.String({ ...SchemaHelper.string.uuid() }), { uniqueItems: true }),

	costAmount: Type.String({ ...SchemaHelper.string.bigint() }),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
