import { Type } from "@sinclair/typebox";

import { SchemaHelper, documentType, createEnum } from "../../../common";

export const documentTypes = createEnum([
	documentType.whs002, // Документ приходная накладная
	documentType.whs009, // Документ акт производства
]);

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	docId: Type.String({ ...SchemaHelper.string.uuid() }),
	docType: Type.Enum(documentTypes),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),
	fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	warehouseId: Type.String({ ...SchemaHelper.string.uuid() }),
	quantity: Type.String({ ...SchemaHelper.string.bigint() }),
	itemCost: Type.String({ ...SchemaHelper.string.bigint() }),
	relevanceDate: Type.String({ ...SchemaHelper.string.date() }),
	supplierTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
};
