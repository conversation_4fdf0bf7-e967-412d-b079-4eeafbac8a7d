import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),

	analogueNomenclatureItemLeftId: Type.String({ ...SchemaHelper.string.uuid() }),
	analogueNomenclatureItemRightId: Type.String({ ...SchemaHelper.string.uuid() }),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
