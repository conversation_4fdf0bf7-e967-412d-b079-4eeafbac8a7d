import { Type } from "@sinclair/typebox";

import { measurementUnit, SchemaHelper } from "../../../common";

export const entity = {
	classifierCategoryId: Type.String({ ...SchemaHelper.string.uuid() }),
	externalCode: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
	edxId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.name() })),
	id: Type.String({ ...SchemaHelper.string.uuid() }),
	internalCode: Type.Integer({ maximum: 9_999_999, minimum: 1_000_000 }),
	isFabricableProduct: Type.Boolean(),
	measure: measurementUnit,
	plannedPrice: Type.String({ ...SchemaHelper.string.bigint() }),
	title: Type.String({ ...SchemaHelper.string.title() }),
	actualBomVersionId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	serialNumberPrefix: SchemaHelper.Nullable(Type.String({ minLength: 1, maxLength: 200 })),
	eskdTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title() })), // обозначение
	eskdFormat: SchemaHelper.Nullable(Type.String({ minLength: 1, maxLength: 8 })),
	description: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.richText() })),
	isCanBeDisassembled: Type.Boolean(),
};
