import { Type } from "@sinclair/typebox";

import { createN<PERSON><PERSON><PERSON><PERSON>, SchemaHelper } from "../../../common";

export const systemStatus = createNumericEnum("OptionAdjustHeaderStatus", {
	1: "draft", // черновик
	2: "accepted", // принято
});

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	fabricatedProductOptionId: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({ ...SchemaHelper.string.title(), minLength: 1 }),
	versionNumber: Type.Number({ ...SchemaHelper.number.nonNegative() }),

	systemStatus: Type.Enum(systemStatus.constEnum),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),

	isDeleted: Type.Boolean(),
	deletedAt: SchemaHelper.Nullable(Type.Number()),
};
