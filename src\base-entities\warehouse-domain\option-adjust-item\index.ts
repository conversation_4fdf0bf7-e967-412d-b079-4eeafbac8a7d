import { Static, Type } from "@sinclair/typebox";

import { createNumericEnum, SchemaHelper, specItemSection } from "../../../common";

export const sourceType = createNumericEnum("OptionAdjustItemSourceType", {
	1: "warehouse",
	2: "fabrication",
	3: "purchase",
});

export type SourceType = keyof typeof sourceType.constEnum;

export const source = Type.Union([
	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType.constEnum["warehouse"]),
		warehouseId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	}),

	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType.constEnum["purchase"]),
	}),

	SchemaHelper.StrictObject({
		type: Type.Literal(sourceType.constEnum["fabrication"]),
		warehouseId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	}),
]);

export type Source = Static<typeof source>;

export const specType = createNumericEnum("OptionAdjustItemSpecType", {
	1: "add",
	2: "remove",
});

export type SpecType = keyof typeof specType.constEnum;

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	headerId: Type.String({ ...SchemaHelper.string.uuid() }),

	specType: Type.Enum(specType.constEnum),
	section: Type.Enum(specItemSection.constEnum),

	source: SchemaHelper.Nullable(source),

	nomenclatureItemId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	docBillOfMaterialHeaderId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	quantity: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })),

	eskdTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title(), minLength: 1 })),
	nonFormalizedTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title(), minLength: 1 })),

	notice: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.trim(), minLength: 1 })),

	options: Type.Array(
		SchemaHelper.StrictObject({
			id: Type.String({ ...SchemaHelper.string.uuid() }),
			headerId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
		}),
		{ uniqueItems: true },
	),
};
