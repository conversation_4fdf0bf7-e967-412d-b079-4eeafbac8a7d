import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	nomenclatureItemId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),
	fabricatedProductId: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.uuid() })),

	prefix: Type.String({ minLength: 1, maxLength: 200 }), // as set in nomenclature item
	increment: Type.Number({ ...SchemaHelper.number.positive() }),

	textValue: Type.String({ minLength: 1 }), // prefix + increment

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
