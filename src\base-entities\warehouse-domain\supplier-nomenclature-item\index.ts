import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	companyId: Type.String({ ...SchemaHelper.string.uuid() }),
	nomenclatureItemId: Type.String({ ...SchemaHelper.string.uuid() }),

	supplierTitle: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.title() })), // Наименование товара у поставщика
	supplierCode: SchemaHelper.Nullable(Type.String({ minLength: 1 })), // Артикул товара у поставщика

	supplierPrice: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })),
	supplierPriceActualAt: SchemaHelper.Nullable(Type.Number()),

	lastPurchasePrice: SchemaHelper.Nullable(Type.String({ ...SchemaHelper.string.bigint() })),
	lastPurchasePriceActualAt: SchemaHelper.Nullable(Type.Number()),

	createdAt: Type.Number(),
	updatedAt: Type.Number(),
};
