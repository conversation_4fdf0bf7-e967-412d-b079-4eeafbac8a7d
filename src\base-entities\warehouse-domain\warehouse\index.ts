import { Type } from "@sinclair/typebox";

import { SchemaHelper } from "../../../common/schema-helper";

export const entity = {
	id: Type.String({ ...SchemaHelper.string.uuid() }),

	departmentId: Type.String({ ...SchemaHelper.string.uuid() }),
	managerId: Type.String({ ...SchemaHelper.string.uuid() }),

	title: Type.String({ ...SchemaHelper.string.title() }),

	isHidden: Type.Boolean(),

	deletedAt: SchemaHelper.Nullable(Type.Number()),
	isDeleted: Type.Boolean(),

	createdAt: Type.Number(),
	updatedAt: SchemaHelper.Nullable(Type.Number()),
};
