import { createEnum } from "./create-enum";

export const domainNames = createEnum([
	"AuthDomain",
	"CalendarDomain",
	"CoordinatorDomain",
	"CrmDomain",
	"DiscussionDomain",
	"EdxDomain",
	"FabricationDomain",
	"FileDomain",
	"HrmDomain",
	"IndexationDomain",
	"JournalDomain",
	"NotificationDomain",
	"OrganizationDomain",
	"PermissionDomain",
	"PrintFormDomain",
	"TaskTrackerDomain",
	"UserDomain",
	"WarehouseDomain",
]);

export const domainMachineNames = createEnum([
	"auth-domain",
	"calendar-domain",
	"coordinator-domain",
	"crm-domain",
	"discussion-domain",
	"edx-domain",
	"fabrication-domain",
	"file-domain",
	"hrm-domain",
	"indexation-domain",
	"journal-domain",
	"notification-domain",
	"organization-domain",
	"permission-domain",
	"print-form-domain",
	"task-tracker-domain",
	"user-domain",
	"warehouse-domain",
]);

export type DomainName = keyof typeof domainNames;
export type DomainMachineName = keyof typeof domainMachineNames;
