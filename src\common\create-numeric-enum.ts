export function createNumericEnum<const T extends { [value: number]: string; }>(
	name: string,
	shape: T,
) {
	const entries = Object
		.entries(shape)
		.map(([value, key]) => [Number(value), key] as const);

	const constEnum = Object.fromEntries(
		entries.map(([, key]) => [key, key]),
	) as {
		readonly [Value in (keyof T & number) as T[Value]]: T[Value];
	};

	const keyValues = Object.fromEntries(
		entries.map(([value, key]) => [key, value]),
	) as {
		readonly [Value in (keyof T & number) as T[Value]]: Value;
	};

	const valueKeys = Object.fromEntries(entries) as T;

	return {
		...keyValues,
		...valueKeys,

		/** Usable for Typebox.Enum. */
		constEnum: Object.freeze(constEnum),
		keyValues: Object.freeze(keyValues),
		/** Not usable for Typebox.Enum. */
		valueKeys: Object.freeze(valueKeys),

		getByKey(key: T[keyof T & number] | (string & NonNullable<unknown>)) {
			if (key in this.keyValues) {
				return this.keyValues[key] as keyof T;
			}

			throw new Error(`Invalid key = '${key}' of ${name}.`);
		},

		getByValue(value: (keyof T & number) | (number & NonNullable<unknown>)) {
			if (value in this.valueKeys) {
				return this.valueKeys[value] as T[keyof T];
			}

			throw new Error(`Invalid value = '${value}' of ${name}.`);
		},
	};
}
