export const availableDataTypes: {
	[key: string]: AvailableDataType;
} = {
	software: {
		machineName: "software",
		name: "Программное обеспечение",
	},
	"user-documentation": {
		machineName: "user-documentation",
		name: "Пользовательская документация",
	},
	"design-documentation": {
		machineName: "design-documentation",
		name: "Конструкторская документация",
	},
} as const;

export const dataType: { [key in keyof typeof availableDataTypes]: key } = {
	software: "software",
	"user-documentation": "user-documentation",
	"design-documentation": "design-documentation",
};

export type AvailableDataType = {
	machineName: string;
	name: string;
};
