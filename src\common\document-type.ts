import { domainNames } from "./available-domains";

export const availableDocuments = {
	crm001: {
		machineName: "act-calculation",
		name: "Калькуляция",
		domain: domainNames.CrmDomain,
	},

	hrm001: {
		machineName: "application-paid-vacation",
		name: "Заявление на отпуск",
		domain: domainNames.HrmDomain,
	},
	hrm002: {
		machineName: "application-unpaid-time-off",
		name: "Заявление на отгул",
		domain: domainNames.HrmDomain,
	},
	hrm004: {
		machineName: "application-overwork",
		name: "Заявление на переработку",
		domain: domainNames.HrmDomain,
	},
	hrm005: {
		machineName: "application-to-head-of-department",
		name: "Заявление руководителю любого подразделения",
		domain: domainNames.HrmDomain,
	},
	hrm006: {
		machineName: "application-business-trip",
		name: "Заявление на командировочные",
		domain: domainNames.HrmDomain,
	},
	hrm007: {
		machineName: "application-sick-leave",
		name: "Заявление на больничный",
		domain: domainNames.HrmDomain,
	},
	hrm008: {
		machineName: "application-add-scheduled-and-track-time",
		name: "Заявление на увеличение регламента и фиксацию времени задачи",
		domain: domainNames.HrmDomain,
	},

	whs001: {
		machineName: "internal-movement",
		name: "Внутреннее перемещение",
		domain: domainNames.WarehouseDomain,
	},
	whs002: {
		machineName: "invoice-purchase",
		name: "Поступление",
		domain: domainNames.WarehouseDomain,
	},
	whs003: {
		machineName: "invoice-sale",
		name: "Реализация",
		domain: domainNames.WarehouseDomain,
	},
	whs004: {
		machineName: "act-inventory",
		name: "Инвентаризация",
		domain: domainNames.WarehouseDomain,
	},
	whs005: {
		machineName: "invoice-inventory-writeoff",
		name: "Списание инвентаризации",
		domain: domainNames.WarehouseDomain,
	},
	whs006: {
		machineName: "invoice-inventory-receipt",
		name: "Поступление инвентаризации",
		domain: domainNames.WarehouseDomain,
	},
	whs007: {
		machineName: "act-combining-nomenclature-items",
		name: "Акт объединения карточек",
		domain: domainNames.WarehouseDomain,
	},
	whs008: {
		machineName: "bill-of-material(deprecated)",
		name: "BOM",
		domain: domainNames.WarehouseDomain,
	},
	whs009: {
		machineName: "act-fabrication",
		name: "Акт производства",
		domain: domainNames.WarehouseDomain,
	},
	whs010: {
		machineName: "act-work-order-purchase",
		name: "Заявка на закупку",
		domain: domainNames.WarehouseDomain,
	},
	whs011: {
		machineName: "act-supplier-consolidated-order",
		name: "Заказ поставщику",
		domain: domainNames.WarehouseDomain,
	},

	fab001: {
		machineName: "work-order",
		name: "Заказ",
		domain: domainNames.FabricationDomain,
	},
	fab002: {
		machineName: "bill-of-material-snapshot",
		name: "Слепок BOM для Заказа",
		domain: domainNames.FabricationDomain,
	},
	fab003: {
		machineName: "act-sent-invoice",
		name: "Выставленный счет",
		domain: domainNames.FabricationDomain,
	},
	fab004: {
		machineName: "act-received-invoice",
		name: "Полученный счет",
		domain: domainNames.FabricationDomain,
	},
	fab005: {
		machineName: "act-purchase(deprecated)",
		name: "Акт закупки",
		domain: domainNames.FabricationDomain,
	},
	fab006: {
		machineName: "act-supplier-consolidated-purchase(deprecated)",
		name: "Закупка",
		domain: domainNames.FabricationDomain,
	},
	fab007: {
		machineName: "act-proposal-collection",
		name: "Проценка",
		domain: domainNames.FabricationDomain,
	},
	fab008: {
		machineName: "act-order-deficit",
		name: "Дефицит Заказа",
		domain: domainNames.FabricationDomain,
	},
	fab009: {
		machineName: "act-fabrication",
		name: "Акт производства",
		domain: domainNames.FabricationDomain,
	},
	fab010: {
		machineName: "act-quality-control",
		name: "Акт прохождения ОТК",
		domain: domainNames.FabricationDomain,
	},
} as const;

export const documentType: { [key in keyof typeof availableDocuments]: key } = {
	crm001: "crm001",

	hrm001: "hrm001",
	hrm002: "hrm002",
	hrm004: "hrm004",
	hrm005: "hrm005",
	hrm006: "hrm006",
	hrm007: "hrm007",
	hrm008: "hrm008",

	whs001: "whs001",
	whs002: "whs002",
	whs003: "whs003",
	whs004: "whs004",
	whs005: "whs005",
	whs006: "whs006",
	whs007: "whs007",
	whs008: "whs008",
	whs009: "whs009",
	whs010: "whs010",
	whs011: "whs011",

	fab001: "fab001",
	fab002: "fab002",
	fab003: "fab003",
	fab004: "fab004",
	fab005: "fab005",
	fab006: "fab006",
	fab007: "fab007",
	fab008: "fab008",
	fab009: "fab009",
	fab010: "fab010",
};
