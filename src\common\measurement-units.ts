import * as TypeBox from "@sinclair/typebox";

export const measurementUnit = TypeBox.Type.Union([
  TypeBox.Type.Literal("003"),
  TypeBox.Type.Literal("004"),
  TypeBox.Type.Literal("006"),
  TypeBox.Type.Literal("112"),
  TypeBox.Type.Literal("163"),
  TypeBox.Type.Literal("166"),
  TypeBox.Type.Literal("796"),
]); // https://classifikators.ru/okei

export type MeasurementUnit = TypeBox.Static<typeof measurementUnit>;

export type MeasurementUnitValue = {
  description?: string;
  fullTitle: string;
  i18nTitle: string;
  title: string;
};

export const measurementUnits: Record<MeasurementUnit, MeasurementUnitValue> = {
  "003": {
    fullTitle: "Миллиметр",
    i18nTitle: "mm",
    title: "мм",
  },
  "004": {
    fullTitle: "Сантиметр",
    i18nTitle: "cm",
    title: "см",
  },
  "006": {
    fullTitle: "Метр",
    i18nTitle: "m",
    title: "м",
  },
  112: {
    fullTitle: "Литр",
    i18nTitle: "l",
    title: "л",
  },
  163: {
    fullTitle: "Грамм",
    i18nTitle: "g",
    title: "г",
  },
  166: {
    fullTitle: "Килограмм",
    i18nTitle: "kg",
    title: "кг",
  },
  796: {
    fullTitle: "Штука",
    i18nTitle: "pc",
    title: "шт",
  },
};
