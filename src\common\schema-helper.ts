import * as Typebox from "@sinclair/typebox";

export class SchemaHelper {
	static NoNNullable = <T extends Typebox.TUnion>(schema: T): T["anyOf"][0] => schema.anyOf[0] as T["anyOf"][0];
	static Nullable = <T extends Typebox.TSchema>(schema: T) => Typebox.Type.Union([schema, Typebox.Type.Null()]);

	static StrictObject = <T extends Typebox.TProperties>(
		properties: T,
		options?: Typebox.ObjectOptions & { additionalProperties?: never; },
	) =>
		Typebox.Type.Object(properties, {
			...options,
			additionalProperties: false,
		});

	static List = <T extends Typebox.TSchema>(schema: T) =>
		SchemaHelper.StrictObject({
			list: Typebox.Type.Array(schema),
			total: Typebox.Type.Number(),
		});

	static constants = {
		MONTH_MAX: 12,
		MONTH_MIN: 1,
		MOVIE_IDS_FOR_REFERENCES_MAX: 15,
		PAGINATION_LIMIT_DEFAULT: 20,
		PAGINATION_LIMIT_MAX: 500,
		PAGINATION_LIMIT_MIN: 1,
		PAGINATION_OFFSET_DEFAULT: 0,
		PAGINATION_OFFSET_MIN: 0,
		SESSION_CODE_MAX: 9999,
		SESSION_CODE_MIN: 1000,
		USER_LOGIN_MAX_LENGTH: 32,
		USER_LOGIN_MIN_LENGTH: 6,
		USER_PASSWORD_MAX_LENGTH: 32,
		USER_PASSWORD_MIN_LENGTH: 8,
	};

	static patterns = {
		COLOR_HEX: "^#(([0-9A-Fa-f]{2}){3,4}|[0-9A-Fa-f]{3})$",
		"E.164": "^\\+[1-9]\\d{10,14}$",
		RU_MOBILE_PHONE_NUMBER: "^\\+79\\d{9}$",
		ONLY_DIGITS: "^[0-9]+$",
		PASSWORD: "^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[~!@#$%^&*()_+={}:;<>,.?\\[\\]\\|\\\\'\"\\-\\/]).{8,}$",
		LTREE: "^[a-z0-9\\-\\.]*[a-z0-9]$",
	};

	static string = {
		bigint: () => ({ isBigint: true }),
		color: () => ({ pattern: this.patterns.COLOR_HEX }),
		date: () => ({ format: "date" }),
		dateTime: () => ({ format: "date-time" }),
		digits: () => ({ pattern: this.patterns.ONLY_DIGITS }),
		email: () => ({ format: "email" }),
		login: () => ({
			maxLength: this.constants.USER_LOGIN_MAX_LENGTH,
			minLength: this.constants.USER_LOGIN_MIN_LENGTH,
		}),
		mobilePhoneMask: () => ({ minLength: 4, transform: ["trim"] }),
		russianMobilePhoneNumber: () => ({ pattern: this.patterns.RU_MOBILE_PHONE_NUMBER }),
		internationalPhoneNumber: () => ({ pattern: this.patterns["E.164"] }),
		name: () => ({ maxLength: 200, minLength: 1, transform: ["trim"] }),
		password: () => ({
			maxLength: this.constants.USER_PASSWORD_MAX_LENGTH,
			minLength: this.constants.USER_PASSWORD_MIN_LENGTH,
			pattern: this.patterns.PASSWORD,
		}),
		pinCode: () => ({
			maxLength: 4,
			minLength: 4,
			pattern: this.patterns.ONLY_DIGITS,
			transform: ["trim"],
		}),
		richText: () => ({ maxLength: 65535, minLength: 1, transform: ["trim"] }),
		richTextExtended: () => ({ maxLength: 5_000_000, minLength: 1, transform: ["trim"] }),
		searchMask: () => ({ minLength: 1, transform: ["trim"] }),
		tag: () => ({ maxLength: 40, minLength: 3, transform: ["trim"] }),
		time: () => ({ format: "time" }),
		title: () => ({ maxLength: 500, minLength: 3, transform: ["trim"] }),
		trim: () => ({ minLength: 1, transform: ["trim"] }),
		uri: () => ({ format: "uri" }),
		uuid: () => ({ format: "uuid" }),
		ipv4: () => ({ format: "ipv4" }),
		ipv6: () => ({ format: "ipv6" }),
		ltree: () => ({ maxLength: 65535, minLength: 1, pattern: this.patterns.LTREE }),
	};

	static number = {
		limit: () => ({
			default: this.constants.PAGINATION_LIMIT_DEFAULT,
			maximum: this.constants.PAGINATION_LIMIT_MAX,
			minimum: this.constants.PAGINATION_LIMIT_MIN,
		}),
		month: () => ({
			maximum: this.constants.MONTH_MAX,
			minimum: this.constants.MONTH_MIN,
		}),
		nonNegative: () => ({ minimum: 0 }),
		offset: () => ({
			default: this.constants.PAGINATION_OFFSET_DEFAULT,
			minimum: this.constants.PAGINATION_OFFSET_MIN,
		}),
		positive: () => ({ minimum: 1 }),
		sessionCode: () => ({
			maximum: this.constants.SESSION_CODE_MAX,
			minimum: this.constants.SESSION_CODE_MIN,
		}),
	};

	static makeCompositeConditionsArray = <T extends Typebox.TSchema>(schema: T) => {
		return Typebox.Type.Optional(SchemaHelper.StrictObject({
			$in: Typebox.Type.Optional(Typebox.Type.Array(schema, { uniqueItems: true, minItems: 1 })),
			$nin: Typebox.Type.Optional(Typebox.Type.Array(schema, { uniqueItems: true, minItems: 1 })),
		}));
	};

	static makeNonEmptyArray = <T extends Typebox.TSchema>(schema: T) => {
		return Typebox.Type.Optional(Typebox.Type.Array(schema, { uniqueItems: true, minItems: 1 }));
	};

	static makeSearchQuery = () => {
		return Typebox.Type.Optional(Typebox.Type.String({ ...SchemaHelper.string.searchMask() }));
	};

	static makeCompositeCondition = <T extends Typebox.TSchema>(schema: T) => {
		return Typebox.Type.Optional(SchemaHelper.StrictObject({
			$eq: Typebox.Type.Optional(schema),
			$ne: Typebox.Type.Optional(schema),
		}));
	};

	static makeGteLteQuery = <T extends Typebox.TSchema>(schema: T) => {
		return Typebox.Type.Optional(SchemaHelper.StrictObject({
			$gte: Typebox.Type.Optional(schema),
			$lte: Typebox.Type.Optional(schema),
		}));
	};

	static listParams = <T extends Record<string, string | number>>(
		orderField: Typebox.TEnum<T>,
		limit: { default: number; maximum: number; minimum: number; } = SchemaHelper.number.limit(),
		offset: { default: number; minimum: number; } = SchemaHelper.number.offset(),
		options?: {
			defaultOrder: { orderDirection: "ASC" | "DESC"; orderField: keyof T; }[];
		},
	) => ({
		limit: Typebox.Type.Number({ ...limit }),
		offset: Typebox.Type.Number({ ...offset }),
		order: Typebox.Type.Optional(Typebox.Type.Array(
			SchemaHelper.StrictObject({
				orderDirection: Typebox.Type.Enum({ ASC: "ASC", DESC: "DESC" } as const),
				orderField,
			}),
			{
				default: options?.defaultOrder || [{ orderDirection: "ASC", orderField: orderField.anyOf?.[0].const || orderField.const }],
				maxItems: orderField.anyOf?.length || 1,
				minItems: 1,
				uniqueItems: true,
			},
		)),
	});

	static listParamsDefault = <T extends Record<string, string | number>>(
		orderField: Typebox.TEnum<T>,
	) => ({
		limit: Typebox.Type.Number({ default: 100, maximum: 1000, minimum: 1 }),
		offset: Typebox.Type.Number(SchemaHelper.number.offset()),
		order: Typebox.Type.Optional(Typebox.Type.Array(
			SchemaHelper.StrictObject({
				orderDirection: Typebox.Type.Enum({ ASC: "ASC", DESC: "DESC" } as const),
				orderField,
			}),
			{
				default: [{ orderDirection: "ASC", orderField: orderField.anyOf?.[0].const || orderField.const }],
				maxItems: orderField.anyOf?.length || 1,
				minItems: 1,
				uniqueItems: true,
			},
		)),
	});
}
