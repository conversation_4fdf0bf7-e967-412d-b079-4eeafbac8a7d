import { createNumericEnum } from "./create-numeric-enum";

export const specItemSection = createNumericEnum("SpecItemSection", {
	1: "documentation", // документация
	2: "complex", // комплекс
	3: "assembly-unit", // сборочная единица
	4: "part", // деталь
	5: "standard-item", // стандартное изделие
	6: "other-item", // прочее изделие
	7: "material", // материал
	8: "kit", // комплект
});

export type SpecItemSection = keyof typeof specItemSection.keyValues;
