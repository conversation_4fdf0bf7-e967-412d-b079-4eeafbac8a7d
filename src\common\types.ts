export type TDataError<T> = { data: T; error?: never; } | { data?: never; error: { code: number; message: string; }; };

export type TUserSystemRole = "admin" | "employee" | "guest";

export type TUserMeta = {
	id: string;
	systemRole: TUserSystemRole;
	roles?: string[];
	ipAddress?: string;
};

export type HttpProtocolApiMeta = {
	user?: TUserMeta;
};

export type NatsProtocolApiMeta = {
	user?: TUserMeta;
	microservice?: {
		name: string;
		ipAddress: string;
		hostName: string;
	};
	requestId?: string;
	transactionId?: string;
};
