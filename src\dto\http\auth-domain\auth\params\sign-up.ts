import { Static, Type } from "@sinclair/typebox";

import * as BaseEntities from "../../../../../base-entities";

export type SignUp = Static<typeof signUp>;

export const signUp = Type.Object({
	firstName: BaseEntities.UserDomain.User.entity.firstName,
	lastName: BaseEntities.UserDomain.User.entity.lastName,
	mobilePhone: BaseEntities.UserDomain.User.entity.mobilePhone,
	password: BaseEntities.AuthDomain.UserPassword.entity.password,
	patronymic: BaseEntities.UserDomain.User.entity.patronymic,
	signUpActivationToken: BaseEntities.AuthDomain.SignUpActivationToken.entity.token,
	tin: BaseEntities.UserDomain.User.entity.tin,
}, { additionalProperties: false });
