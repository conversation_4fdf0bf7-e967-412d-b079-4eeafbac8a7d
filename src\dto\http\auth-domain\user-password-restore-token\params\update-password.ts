import { Static, Type } from "@sinclair/typebox";

import * as BaseEntities from "../../../../../base-entities";

export const updatePassword = Type.Object({
	password: BaseEntities.AuthDomain.UserPassword.entity.password,
	userPasswordRestoreToken: BaseEntities.AuthDomain.UserPasswordRestoreToken.entity.token,
}, { additionalProperties: false });

export type UpdatePassword = Static<typeof updatePassword>;
