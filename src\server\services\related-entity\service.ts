import * as InwaveTypes from "@2people-it/inwave-erp-types";
import { NotificationType } from "@2people-it/inwave-erp-types/dist/base-entities/notification-domain/common/notification-type.js";
import { availableEntities } from "@2people-it/inwave-erp-types/dist/base-entities/available-entities.js";

import * as Types from "../../types/index.js";

import BaseService from "../base-service.js";

export type FileRelationEntityType =
	keyof typeof InwaveTypes.BaseEntities.FileDomain.File.availableRelativeEntityType;

export default class Service extends BaseService {
	#businessError;
	#broker;
	#logger;

	#microserviceMeta;
	#IS_TEST;

	constructor(data: {
		businessError: Types.System.BusinessError.Service;
		config: Types.Config.ConfigOptions;
		broker: Types.Broker.default;
		logger: Types.System.Logger.Service;
	}) {
		super();

		this.#businessError = data.businessError;
		this.#broker = data.broker;
		this.#logger = data.logger;

		this.#microserviceMeta = {
			hostName: data.config.SYSTEM_HOSTNAME,
			ipAddress: data.config.SYSTEM_IP_ADDRESS,
			name: InwaveTypes.NatsProtocol.Domains.FileDomain.domainName,
		};

		this.#IS_TEST = data.config.IS_TEST;
	}

	checkDocBillOfMaterialHeaders = async (params: {
		ids: string[];
	}): Promise<Types.Common.TDataError<true>> => {
		let offset = 0;
		const limit = 500;
		const totalHeaders = [];

		while (true) {
			const response = await this.#broker.domains.warehouse.fetch({
				methodName:
					InwaveTypes.NatsProtocol.Domains.WarehouseDomain
						.DocBillOfMaterialHeader.GetList.method.name,
				params: {
					filters: {
						ids: params.ids,
					},
					limit,
					offset,
					order: [{ orderField: "id", orderDirection: "ASC" }],
				},
			});

			const headers = response.list;

			totalHeaders.push(...headers);

			if (headers.length < limit) break;

			offset += limit;
		}

		if (totalHeaders.length !== params.ids.length) {
			return this.#businessError.error.ENTITY_NOT_FOUND(
				"NOT_FOUND_DOC_BILL_OF_MATERIAL_HEADER",
			);
		}

		return { data: true };
	};

	// ToDo: пока используется только для проверки ACL
	// см. комментарий в src/server/utils/acl/get-user-actor-ids.ts
	getUserDepartments = async (params: {
		userId: string;
	}): Promise<string[]> => {
		const response = await this.#broker.domains.user.fetch({
			methodName:
				InwaveTypes.NatsProtocol.Domains.UserDomain.User.GetEntity.method.name,
			params: {
				id: params.userId,
			},
		});

		return response.departmentData.map((department) => department.departmentId);
	};

	copyFileRelations = async (params: {
		entityType: FileRelationEntityType;
		fromEntityId: string;
		toEntityId: string;
	}): Promise<void> => {
		await this.#broker.domains.file.fetch({
			methodName:
				InwaveTypes.NatsProtocol.Domains.FileDomain.File.CopyRelations.method
					.name,
			params: {
				entityType: params.entityType,
				fromEntityId: params.fromEntityId,
				toEntityId: params.toEntityId,
			},
			meta: {
				microservice: this.#microserviceMeta,
			},
		});
	};

	// HRM Domain methods
	getHrmApplicationPaidVacationList = async (params: {
		filters: {
			authorIds?: { $in: string[]; };
			ids?: string[];
			dateFrom?: number;
			systemStatuses?: { $in: string[]; };
		};
		limit: number;
		offset: number;
		order?: Array<{ orderField: string; orderDirection: string; }>;
	}) => {
		return await this.#broker.domains.hrmDomain.fetch({
			methodName: "hrm-domain:application-paid-vacation/get-list",
			params,
		});
	};

	getHrmApplicationSickLeaveList = async (params: {
		filters: {
			authorIds?: { $in: string[]; };
			ids?: string[];
			dateFrom?: number;
			systemStatuses?: { $in: string[]; };
		};
		limit: number;
		offset: number;
		order?: Array<{ orderField: string; orderDirection: string; }>;
	}) => {
		return await this.#broker.domains.hrmDomain.fetch({
			methodName: "hrm-domain:application-sick-leave/get-list",
			params,
		});
	};

	getHrmApplicationUnpaidTimeOffList = async (params: {
		filters: {
			authorIds?: { $in: string[]; };
			ids?: string[];
			dateFrom?: number;
			systemStatuses?: { $in: string[]; };
		};
		limit: number;
		offset: number;
		order?: Array<{ orderField: string; orderDirection: string; }>;
	}) => {
		return await this.#broker.domains.hrmDomain.fetch({
			methodName: "hrm-domain:application-unpaid-time-off/get-list",
			params,
		});
	};

	getHrmApplicationOverworkList = async (params: {
		filters: {
			authorIds?: { $in: string[]; };
			ids?: string[];
			dateFrom?: number;
			systemStatuses?: { $in: string[]; };
		};
		limit: number;
		offset: number;
		order?: Array<{ orderField: string; orderDirection: string; }>;
	}) => {
		return await this.#broker.domains.hrmDomain.fetch({
			methodName: "hrm-domain:application-overwork/get-list",
			params,
		});
	};

	getHrmApplicationOverworkPreparedList = async (params: {
		filters: {
			authorIds?: { $in: string[]; };
			systemStatuses?: { $in: string[]; };
			dateFrom?: number;
		};
		limit: number;
		offset: number;
		order?: Array<{ orderField: string; orderDirection: string; }>;
	}) => {
		return await this.#broker.domains.hrmDomain.fetch({
			methodName: "hrm-domain:application-overwork/get-prepared-list",
			params,
		});
	};

	getHrmApplicationAddScheduledAndTrackTimeList = async (params: {
		filters: {
			ids: string[];
		};
		limit: number;
		offset: number;
	}) => {
		return await this.#broker.domains.hrmDomain.fetch({
			methodName: "hrm-domain:application-add-scheduled-and-track-time/get-list",
			params,
		});
	};
}
