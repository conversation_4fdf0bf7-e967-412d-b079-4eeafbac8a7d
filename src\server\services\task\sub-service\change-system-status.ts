import { Common } from "@2people-it/inwave-erp-types";
import { TaskTrackerDomain } from "@2people-it/inwave-erp-types/dist/protocols/nats/domains/index.js";

import * as Types from "../../../types/index.js";

import * as MasterClass from "../service.js";

import { Enum } from "../index.js";

export class Service {
	#masterService;

	#businessError;
	#logger;
	#transactions;
	#repository;
	#response;

	constructor(masterService: MasterClass.default) {
		this.#masterService = masterService;

		this.#businessError = masterService.businessError;
		this.#logger = masterService.logger;
		this.#transactions = masterService.transactions;
		this.#repository = masterService.repository;
		this.#response = masterService.response;
	}

	async #checkHasActiveApplications(
		taskId: string,
	): Promise<Types.Common.TDataError<true>> {
		if (this.#masterService.config.IS_TEST) {
			return {
				data: true,
			};
		}

		const { total } = await this.#masterService.broker.domains.hrmDomain.fetch({
			methodName:
				"hrm-domain:application-add-scheduled-and-track-time/get-prepared-list",
			params: {
				filters: {
					taskIds: [taskId],
					systemStatuses: { $in: ["in-progress"] },
				},
				limit: 500,
				offset: 0,
			},
		});

		if (total > 0) {
			return this.#businessError.error.UNKNOWN_ERROR(
				"HAS_ACTIVE_HRM_APPLICATIONS",
			);
		} else
			return {
				data: true,
			};
	}

	async #determineMetaUserStatus(
		payload: TaskTrackerDomain.Task.ChangeSystemStatus.Params,
		meta?: Common.Types.NatsProtocolApiMeta,
	): Promise<
		Types.Common.TDataError<{
			isMicroservice: boolean;
			isAdmin: boolean;
			isAuthor: boolean;
			isAcceptor: boolean;
			isAdminOrAuthor: boolean;
			isAdminOrAuthorOrAcceptor: boolean;
			isPerformer: boolean;
		}>
	> {
		if (meta?.microservice) {
			return {
				data: {
					isMicroservice: true,
					isAdmin: false,
					isAuthor: false,
					isAcceptor: false,
					isAdminOrAuthor: false,
					isAdminOrAuthorOrAcceptor: false,
					isPerformer: false,
				},
			};
		} else if (meta?.user) {
			const { id } = payload;

			const { data: initiator, error }
				= await this.#masterService.innerSpace.checkAcl(
					{ id, method: "changeSystemStatus" },
					meta,
				);

			if (error) return { error };

			const involvedUsers = await this.#repository.getInvolvedUsers({ id });

			const isAdmin = initiator.systemRole === "admin";
			const isAuthor = initiator.id === involvedUsers.authorId;
			const isAcceptor = involvedUsers.acceptors.has(initiator.id);
			const isAdminOrAuthor = isAdmin || isAuthor;
			const isAdminOrAuthorOrAcceptor = isAdminOrAuthor || isAcceptor;
			const isPerformer = initiator.id === involvedUsers.performerId;

			return {
				data: {
					isMicroservice: false,
					isAdmin,
					isAuthor,
					isAcceptor,
					isAdminOrAuthor,
					isAdminOrAuthorOrAcceptor,
					isPerformer,
				},
			};
		} else {
			return this.#businessError.error.ACCESS_DENIED();
		}
	}

	async #check(
		payload: TaskTrackerDomain.Task.ChangeSystemStatus.Params,
		meta?: Common.Types.NatsProtocolApiMeta,
	): Promise<Types.Common.TDataError<Types.Dal.Task.Types.Entity>> {
		const metaUserStatus = await this.#determineMetaUserStatus(payload, meta);

		if (metaUserStatus.error) return { error: metaUserStatus.error };

		const {
			isAdminOrAuthor,
			isAdminOrAuthorOrAcceptor,
			isMicroservice,
			isPerformer,
		} = metaUserStatus.data;

		const { id, systemStatus } = payload;

		const entity = await this.#masterService.innerSpace.getEntity({
			id,
		});

		if (entity.error) return { error: entity.error };

		const systemStatusEnum = this.#masterService.innerSpace.enum.systemStatus;

		if (entity.data.system_status === systemStatusEnum[systemStatus]) {
			return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
		}

		if (systemStatus !== "planned" && !entity.data.performer_id) {
			return this.#businessError.error.TASK_PERFORMER_IS_NOT_DEFINED();
		}

		switch (entity.data.system_status) {
			case systemStatusEnum.planned: {
				switch (systemStatus) {
					// Запланирована -> Назначена
					case "assigned": {
						if (!isAdminOrAuthorOrAcceptor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						if (entity.data.is_fixed) {
							const { error }
								= await this.#masterService.innerSpace.checkFixedTaskHasOverlaps({
									task: entity.data,
								});

							if (error) {
								return {
									error,
								};
							}
						}

						break;
					}

					case "started": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}

				break;
			}

			case systemStatusEnum.assigned: {
				switch (systemStatus) {
					// Назначена -> Запланирована
					case "planned": {
						if (!isAdminOrAuthorOrAcceptor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// Назначена -> В работе
					case "in-progress": {
						if (!isAdminOrAuthorOrAcceptor && !isPerformer) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						// Проверка на блокирующие задачи (все должны быть со статусом закрыта)
						const { error } = await this.#checkBlockingForClosed({
							taskId: id,
						});

						if (error) return { error };

						{
							const { error }
								= await this.#checkAnotherNormativeTasksInProgress({
									taskId: id,
									performerId: entity.data.performer_id,
								});

							if (error) return { error };
						}

						{
							const { error } = await this.#checkNormativeTaskHasScheduledTime(
								entity.data,
							);

							if (error) return { error };
						}

						break;
					}

					// Назначена -> В процессе выполнения
					case "started": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}

				break;
			}

			case systemStatusEnum["in-progress"]: {
				switch (systemStatus) {
					// В работе -> Запланирована/Заблокирована
					case "planned":
					case "blocked": {
						if (!isAdminOrAuthorOrAcceptor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// В работе -> Назначена/На паузе/Выполнена
					case "assigned":
					case "paused":
					case "completed": {
						if (
							!this.#masterService.config.IS_TEST
							&& new Date(entity.data.updated_at || 0)
							> new Date(Date.now() - 1 * 60 * 1000)
						) {
							return this.#businessError.error.UNKNOWN_ERROR(
								"IMPOSSIBLE_TO_CHANGE_SYSTEM_STATUS_MORE_THAN_ONCE_PER_MINUTE",
							);
						}

						if (!isAdminOrAuthorOrAcceptor && !isPerformer) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// В работе -> В процессе выполнения
					case "started": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}

				break;
			}

			case systemStatusEnum.blocked: {
				switch (systemStatus) {
					// Заблокирована -> Запланирована
					case "planned": {
						if (!isAdminOrAuthorOrAcceptor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// Заблокирована -> В работе
					case "in-progress": {
						if (!isAdminOrAuthorOrAcceptor && !isPerformer) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						// Проверка на блокирующие задачи (все должны быть со статусом закрыта)
						const { error } = await this.#checkBlockingForClosed({
							taskId: id,
						});

						if (error) return { error };

						break;
					}

					// Заблокирована -> В процессе выполнения
					case "started": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}

				break;
			}

			case systemStatusEnum.paused: {
				switch (systemStatus) {
					// На паузе -> Запланирована
					case "planned": {
						if (!isAdminOrAuthorOrAcceptor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// На паузе -> Назначена/В работе
					case "assigned":
					case "in-progress": {
						if (!isAdminOrAuthorOrAcceptor && !isPerformer) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						// Проверка на блокирующие задачи (все должны быть со статусом закрыта)
						{
							const { error } = await this.#checkBlockingForClosed({
								taskId: id,
							});

							if (error) return { error };
						}

						{
							const { error }
								= await this.#checkAnotherNormativeTasksInProgress({
									taskId: id,
									performerId: entity.data.performer_id,
								});

							if (error) return { error };
						}

						{
							const { error } = await this.#checkNormativeTaskHasScheduledTime(
								entity.data,
							);

							if (error) return { error };
						}

						break;
					}

					// На паузе -> В процессе выполнения
					case "started": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}

				break;
			}

			case systemStatusEnum.completed: {
				switch (systemStatus) {
					// Выполнена -> Запланирована
					case "planned": {
						if (!isAdminOrAuthorOrAcceptor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// Выполнена -> Назначена
					case "assigned": {
						if (!isAdminOrAuthorOrAcceptor && !isPerformer) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						break;
					}

					// Выполнена -> Закрыта
					case "closed": {
						if (!isMicroservice && !isAdminOrAuthor) {
							return this.#businessError.error.ACCESS_DENIED();
						}

						{
							const isAccepted
								= await this.#masterService.innerSpace.isAccepted({
									taskId: entity.data.id,
								});

							if (!isAccepted) {
								return this.#businessError.error.UNKNOWN_ERROR(
									"MUST_BE_ACCEPTED",
								);
							}
						}

						// Если у задачи есть чек-лист (чтобы все пункты были выполнены)
						if (entity.data.checklist) {
							const isNotDone = entity.data.checklist.find((e) => !e.isDone);

							if (isNotDone) {
								return this.#businessError.error.ACCESS_DENIED();
							}
						}

						{
							// Проверка потомков (все должны быть со статусом закрыта)
							const { error } = await this.#checkChildrenForClosed({
								path: entity.data.path,
								taskThreadId: entity.data.tt_id,
							});

							if (error) return { error };
						}

						{
							// Проверка на отсутствие открытых заявлений, связанных с задачей
							const { error } = await this.#checkHasActiveApplications(
								entity.data.id,
							);

							if (error) {
								return {
									error,
								};
							}
						}

						break;
					}

					// Выполнена -> В процессе выполнения/Отклонена
					case "started":
					case "declined": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}
				break;
			}

			case systemStatusEnum.closed: {
				switch (systemStatus) {
					// Закрыта -> Назначена
					case "assigned": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}
			}

			case systemStatusEnum.started: {
				if (!isMicroservice) {
					return this.#businessError.error.ACCESS_DENIED();
				}

				break;
			}

			case systemStatusEnum.declined: {
				switch (systemStatus) {
					// Отклонена -> Назначена
					case "assigned": {
						break;
					}

					default: {
						return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
					}
				}
			}

			default: {
				return this.#businessError.error.TASK_INVALID_SYSTEM_STATUS();
			}
		}

		return { data: entity.data };
	}
	async #checkAnotherNormativeTasksInProgress(payload: {
		taskId: string;
		performerId: string | null;
	}): Promise<Types.Common.TDataError<true>> {
		const { performerId, taskId } = payload;

		if (performerId) {
			// Проверка на другие нормативные задачи в статусе inProgress
			const { data } = await this.#getAnotherNormativeTasksInProgress({
				taskId,
				performerId,
			});

			const isAnotherTaskUpdatedInLastMinuteExists
				= data?.length
				&& data.some(
					(anotherTask) =>
						new Date(anotherTask.updated_at || 0)
						> new Date(Date.now() - 1 * 60 * 1000),
				);

			if (
				isAnotherTaskUpdatedInLastMinuteExists
				&& !this.#masterService.config.IS_TEST
			) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"ANOTHER_NORMATIVE_TASK_WAS_STARTED_LESS_THAN_MINUTE_AGO",
				);
			}
		}

		return { data: true };
	}
	async #checkNormativeTaskHasScheduledTime(payload: {
		is_normative: boolean;
		scheduled_time: number | null;
		spent_time: number;
	}): Promise<Types.Common.TDataError<true>> {
		const { is_normative, scheduled_time, spent_time } = payload;

		if (is_normative) {
			const remainingTaskDuration = (scheduled_time || 0) - spent_time;

			if (remainingTaskDuration <= 0) {
				return this.#businessError.error.UNKNOWN_ERROR(
					"IMPOSSIBLE_TO_PROGRESS_TASK_WITH_LEFT_SCHEDULED_TIME",
				);
			}
		}

		return { data: true };
	}

	async #checkBlockingForClosed(data: {
		taskId: string;
	}): Promise<Types.Common.TDataError<true>> {
		const { blockingLinkTaskIds }
			= await this.#masterService.services.taskBlockingLink.innerSpace.getNeighbors(
				{
					taskId: data.taskId,
				},
			);

		if (blockingLinkTaskIds.size) {
			const systemStatusEnum = this.#masterService.innerSpace.enum.systemStatus;

			const exists = await this.#masterService.repository.checkUnclosedTask({
				ids: [...blockingLinkTaskIds],
				systemStatus: { closed: systemStatusEnum.closed },
			});

			if (exists) {
				return this.#businessError.error.TASK_CHILD_NOT_CLOSED_YET();
			}
		}

		return { data: true };
	}

	async #getAnotherNormativeTasksInProgress(data: {
		taskId: string;
		performerId: string;
	}): Promise<
		Types.Common.TDataError<
			Pick<
				Types.Dal.Task.Types.TableFields,
				| "id"
				| "performer_id"
				| "task_thread_id"
				| "path"
				| "spent_time"
				| "started_at"
				| "updated_at"
			>[]
		>
	> {
		const { performerId, taskId } = data;
		const anotherNormativeTasks
			= await this.#masterService.repository.getArrByParams({
				params: {
					is_deleted: false,
					id: { $ne: taskId },
					performer_id: performerId,
					system_status: Enum.SystemStatus.default["in-progress"],
					is_normative: true,
				},
				selected: [
					"id",
					"performer_id",
					"spent_time",
					"started_at",
					"path",
					"task_thread_id",
					"updated_at",
				],
			});

		return { data: anotherNormativeTasks };
	}

	async #processNormativeTask(payload: {
		taskId: string;
		performerId: string;
		systemStatus: number;
	}) {
		const { performerId, systemStatus, taskId } = payload;

		const systemStatusEnum = this.#masterService.innerSpace.enum.systemStatus;

		switch (systemStatusEnum.findByValue(systemStatus)) {
			// Если нормативная задача переведена в статус
			// "Назначена"
			case "assigned": {
				// Необходимо добавить задачу в конец локальной очереди пользователя (если она не фиксированная)
				// Или рассчитать фиксированной задаче время начала и время окончания
				await this.#masterService.services.performerNormativeTask.innerSpace.updatePerformerSchedule(
					{
						action: "push",
						task: { id: taskId, performerId },
					},
				);

				break;
			}

			// Если нормативная задача переведена в статус "В работе"
			// "В работе"
			case "in-progress": {
				// Необходимо все остальные активные задачи пользователя поставить на паузу + обновить spent_time
				// Если она фиксированная то сбросить флаг фиксированности и убрать фиксированное время старта
				await this.#masterService.services.queueManager.queues[
					"sync-normative-tasks-in-progress"
				].exec({
					task: { id: taskId, performerId },
				});

				break;
			}

			// Если нормативная задача переведена в статус
			// "Запланирована"
			// "Выполнена"
			// "Закрыта"
			case "planned":
			case "completed":
			case "closed": {
				// Необходимо удалить задачу из локальной очереди пользователя
				// Необходимо пересчитать локальную очередь пользователя
				await this.#masterService.services.performerNormativeTask.innerSpace.remove(
					{
						task: { id: taskId, performerId },
					},
				);

				break;
			}

			case "paused": {
				await this.#masterService.services.performerNormativeTask.innerSpace.sync(
					{ task: { id: taskId, performerId } },
				);

				break;
			}

			case "blocked": {
				break;
			}

			default: {
				throw new Error("Invalid status");
			}
		}
	}

	async #checkChildrenForClosed(data: {
		path: string;
		taskThreadId: string;
	}): Promise<Types.Common.TDataError<true>> {
		const systemStatusEnum = this.#masterService.innerSpace.enum.systemStatus;

		const exists = await this.#masterService.repository.checkUnclosedTask({
			path: data.path,
			systemStatus: systemStatusEnum,
			taskThreadId: data.taskThreadId,
		});

		if (exists) {
			return this.#businessError.error.TASK_CHILD_NOT_CLOSED_YET();
		}

		return { data: true };
	}

	async getResponse(
		id: string,
	): Promise<
		Types.Common.TDataError<TaskTrackerDomain.Task.ChangeSystemStatus.Result>
	> {
		const result = await this.#masterService.innerSpace.getEntity({ id });

		if (result.error) return { error: result.error };

		return { data: this.#response.getEntity(result.data) };
	}

	async exec(
		payload: TaskTrackerDomain.Task.ChangeSystemStatus.Params,
		meta?: Common.Types.NatsProtocolApiMeta,
	): Promise<
		Types.Common.TDataError<TaskTrackerDomain.Task.ChangeSystemStatus.Result>
	> {
		const check = await this.#check(payload, meta);

		if (check.error) return { error: check.error };

		const taskForCheck = check.data;

		const closedAt = payload.systemStatus === "closed" ? Date.now() : undefined;
		const startedAt
			= payload.systemStatus === "in-progress" ? Date.now() : null;

		const currentDate = new Date();

		const spentTimeMins
			= taskForCheck.is_normative
				&& taskForCheck.started_at // если есть started_at значит задача на стадии in-progress
				&& taskForCheck.performer_id
				? await this.#masterService.innerSpace.calculateSpentTimeMins({
					startedAt: taskForCheck.started_at,
					performerId: taskForCheck.performer_id,
					currentDate,
				})
				: undefined;

		const spentTimeResult = spentTimeMins
			? taskForCheck.spent_time + spentTimeMins
			: undefined;

		await this.#masterService.innerSpace.update({
			...payload,
			closedAt,
			startedAt,
			spentTime: spentTimeResult,
		});

		if (payload.systemStatus === "completed") {
			await this.#masterService.innerSpace.unacceptAllAcceptors({
				taskId: payload.id,
			});
		}

		const isAccepted = await this.#masterService.innerSpace.isAccepted({
			taskId: payload.id,
		});

		if (isAccepted) {
			await this.#masterService.outerSpace
				.changeSystemStatus(
					{
						id: payload.id,
						systemStatus: "closed",
					},
					this.#masterService.microserviceMeta,
				)
				.catch((e) => this.#logger.warn(e.message));
		}

		if (spentTimeMins) {
			await this.#masterService.services.performerNormativeTaskSpentTime.innerSpace.create(
				{
					endDateTime: currentDate,
					performerId: taskForCheck.performer_id as string,
					spentTimeMins,
					startDateTime: taskForCheck.started_at as Date,
					taskId: taskForCheck.id,
				},
			);

			await this.#masterService.services.unreadIndicator.innerSpace.register({
				actorId: meta?.user?.id || null,
				counter: 1,
				entityId: taskForCheck.id,
				entityType: "task",
				eventType: "spent-time-changed",
			});
		}

		await this.#transactions[
			"task-update-overdue-and-under-risk-overdue-flags"
		]();

		const { data: result, error }
			= await this.#masterService.innerSpace.getEntity({
				id: payload.id,
			});

		if (error) return { error };

		await this.#handleNotifications({
			authorId: meta?.user?.id || null,
			task: result,
		});

		if (taskForCheck.is_normative) {
			if (!taskForCheck.performer_id)
				throw new Error("taskForCheck.performer_id not defined");

			await this.#processNormativeTask({
				performerId: taskForCheck.performer_id,
				taskId: result.id,
				systemStatus: result.system_status,
			});

			{
				const { error }
					= await this.#repository.updateNormativeTaskParentTimesAndTotalPlannedDates(
						{
							taskId: result.id,
							taskPath: result.path,
							taskThreadId: result.tt_id,
						},
					);

				if (error) {
					return {
						error,
					};
				}
			}

			await this.#transactions[
				"task-update-overdue-and-under-risk-overdue-flags"
			]();

			return this.getResponse(payload.id);
		}

		return { data: this.#response.getEntity(result) };
	}

	async #handleNotifications({
		authorId,
		task,
	}: {
		task: Types.Dal.Task.Types.Entity;
		authorId: string | null;
	}) {
		const systemStatusEnum = this.#masterService.innerSpace.enum.systemStatus;

		const isStatusCompleted
			= systemStatusEnum.findByValue(task.system_status) === "completed";
		const isStatusClosed
			= systemStatusEnum.findByValue(task.system_status) === "closed";

		const statusChangedRecipientIdSet = new Set(task.tor_observer_ids);

		if (isStatusCompleted) {
			const acceptorIds = task.acceptors.map((e) => e.acceptor_id);

			if (acceptorIds.length) {
				this.#masterService.services.notificationPublisher.innerSpace.send({
					message: "Задача ожидает акцептования",
					recipientIds: acceptorIds,
					notificationType: "task-tracker:task-accept-new",
					entityType: "task",
					entityId: task.id,
				});
			}

			acceptorIds.forEach((acceptorId) => {
				statusChangedRecipientIdSet.delete(acceptorId);
			});
		}

		if (isStatusClosed) {
			await this.#handleClosedStatusNotifications(task, authorId);
		}

		if (task.performer_id) {
			statusChangedRecipientIdSet.add(task.performer_id);
		}

		authorId && statusChangedRecipientIdSet.delete(authorId);

		if (!statusChangedRecipientIdSet.size) return;

		this.#masterService.services.notificationPublisher.innerSpace.send({
			message: "Изменён статус задачи",
			recipientIds: [...statusChangedRecipientIdSet],
			notificationType: "task-tracker:task-status-change",
			entityType: "task",
			entityId: task.id,
		});
	}

	async #handleClosedStatusNotifications(
		task: Types.Dal.Task.Types.Entity,
		authorId: string | null,
	) {
		try {
			const { data, error }
				= await this.#masterService.innerSpace.getBlockingRelations(task.id);

			if (error) return { error };

			const { tbl_chains } = data;

			const { blockedLinkTaskIds }
				= this.#masterService.services.taskBlockingLink.innerSpace.sortNeighbors({
					neighbors: tbl_chains,
					taskId: task.id,
				});

			const promises = Array.from(blockedLinkTaskIds).map(async (taskId) => {
				const { data, error } = await this.#checkIfTaskIsUnblocked(taskId);

				if (error) return;

				if (!data.isAllClosed) {
					return;
				}

				const recipientIdSet = new Set(data.task.tor_observer_ids);

				if (data.task.performer_id) {
					recipientIdSet.add(data.task.performer_id);
				}

				authorId && recipientIdSet.delete(authorId);

				if (!recipientIdSet.size) return;

				await this.#masterService.services.notificationPublisher.innerSpace.send(
					{
						message: "Задача разблокирована",
						recipientIds: [...recipientIdSet],
						notificationType: "task-tracker:task-unlock",
						entityType: "task",
						entityId: taskId,
					},
				);
			});

			await Promise.all(promises);
		} catch (e) {
			this.#masterService.logger.warn(
				"Error in handleClosedStatusNotifications of change-system-status: "
				+ e,
			);
		}
	}

	async #checkIfTaskIsUnblocked(taskId: string): Promise<
		Types.Common.TDataError<{
			isAllClosed: boolean;
			task: Types.Dal.Task.Types.Entity;
		}>
	> {
		const { data: task, error: entityError }
			= await this.#masterService.innerSpace.getEntity({ id: taskId });

		if (entityError) return { error: entityError };

		const { data, error }
			= await this.#masterService.innerSpace.getBlockingRelations(taskId);

		if (error) return { error };

		const { tbl_chains } = data;

		const { blockingLinkTaskIds }
			= this.#masterService.services.taskBlockingLink.innerSpace.sortNeighbors({
				neighbors: tbl_chains,
				taskId,
			});

		const isAllClosed = await this.#masterService.repository.checkIsAllClosed({
			taskIds: [...blockingLinkTaskIds],
		});

		return { data: { isAllClosed, task } };
	}
}
