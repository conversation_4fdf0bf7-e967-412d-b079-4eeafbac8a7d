import * as TaskTrackerDomain from "../../base-entities/task-tracker-domain";

export const entityTranslations: Record<
	keyof typeof TaskTrackerDomain.availableEntities,
	string
> = {
	directory: "Папка",
	tag: "Тэг",
	task: "Задача",
	"task-thread": "Лист",
	workspace: "Хранилище",
	"task-cluster-template": "Шаблон задач",
};

export const taskSystemStatus: Record<
	keyof typeof TaskTrackerDomain.Task.systemStatus,
	string
> = {
	planned: "Запланирована",
	assigned: "Назначена",
	paused: "На паузе",
	blocked: "Заблокирована",
	started: "В процессе выполнения",
	"in-progress": "В работе",
	completed: "Выполнена",
	closed: "Закрыта",
	declined: "Отклонена",
};

export const taskPriority: Record<
	keyof typeof TaskTrackerDomain.Task.priority,
	string
> = {
	"very-hight": "Очень высокий",
	hight: "Высокий",
	medium: "Средний",
	normal: "Обычный",
	low: "Низкий",
};

export const taskClusterTemplateTypes: Record<
	keyof typeof TaskTrackerDomain.TaskClusterTemplate.taskClusterTemplate,
	string
> = {
	directory: "Папка",
	task: "Задача",
	"task-thread": "Лист",
	workspace: "Хранилище",
};
